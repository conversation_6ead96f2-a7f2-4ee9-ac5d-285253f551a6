(()=>{var e={};e.id=5092,e.ids=[5092,660],e.modules={8461:e=>{e.exports={analyticsContainer:"BookingAnalytics_analyticsContainer__9mwvM",analyticsHeader:"BookingAnalytics_analyticsHeader__xLMhj",timeRangeSelector:"BookingAnalytics_timeRangeSelector__PDmBu",active:"BookingAnalytics_active__TxOIw",metricsGrid:"BookingAnalytics_metricsGrid__RhMfg",metricCard:"BookingAnalytics_metricCard__Wulh9",metricValue:"BookingAnalytics_metricValue__y1B6e",metricLabel:"BookingAnalytics_metricLabel__8Zt27",chartsGrid:"BookingAnalytics_chartsGrid__j_qu3",chartCard:"BookingAnalytics_chartCard__MnasY",statusChart:"BookingAnalytics_statusChart__nf1N9",statusItem:"BookingAnalytics_statusItem__7JNm3",statusIndicator:"BookingAnalytics_statusIndicator__CYo8r",statusLabel:"BookingAnalytics_statusLabel__Hd5ye",statusCount:"BookingAnalytics_statusCount__uFsxj",topList:"BookingAnalytics_topList__AZWQO",topItem:"BookingAnalytics_topItem__zZAmz",topRank:"BookingAnalytics_topRank__P6LSm",topInfo:"BookingAnalytics_topInfo__cTaK_",topName:"BookingAnalytics_topName__Rp2A8",topStats:"BookingAnalytics_topStats__0sP2c",trendChart:"BookingAnalytics_trendChart__Y5IeY",trendItem:"BookingAnalytics_trendItem__2BYPl",trendMonth:"BookingAnalytics_trendMonth__yqawq",trendBookings:"BookingAnalytics_trendBookings__jtFHq",trendRevenue:"BookingAnalytics_trendRevenue__Haw3F",emptyState:"BookingAnalytics_emptyState__cVXz4"}},9113:e=>{e.exports={calendarContainer:"BookingCalendar_calendarContainer__gwvLi",calendarHeader:"BookingCalendar_calendarHeader__6KhH7",monthNavigation:"BookingCalendar_monthNavigation___DEjo",navButton:"BookingCalendar_navButton__g2PdZ",monthTitle:"BookingCalendar_monthTitle__DiK7E",todayButton:"BookingCalendar_todayButton__Lht_7",calendar:"BookingCalendar_calendar__9Rf9A",dayHeaders:"BookingCalendar_dayHeaders__gEDwd",dayHeader:"BookingCalendar_dayHeader__zSjjo",calendarGrid:"BookingCalendar_calendarGrid__umy5o",calendarDay:"BookingCalendar_calendarDay__fFkuf",today:"BookingCalendar_today__ItoLp",otherMonth:"BookingCalendar_otherMonth__qwT2U",dayNumber:"BookingCalendar_dayNumber__bJmAK",bookingsContainer:"BookingCalendar_bookingsContainer__c_ltx",bookingItem:"BookingCalendar_bookingItem__Ex83v",bookingTime:"BookingCalendar_bookingTime__XlFnR",bookingCustomer:"BookingCalendar_bookingCustomer__Gbocf",bookingService:"BookingCalendar_bookingService__by0T_",moreBookings:"BookingCalendar_moreBookings__RymFm"}},8704:e=>{e.exports={bookingsContainer:"Bookings_bookingsContainer__LxQns",header:"Bookings_header__LPpER",title:"Bookings_title__kkjQ_",headerActions:"Bookings_headerActions__oOFo9",newBookingBtn:"Bookings_newBookingBtn__DThjN",backButton:"Bookings_backButton__d7v8l",controlsPanel:"Bookings_controlsPanel__OqLxe",viewToggle:"Bookings_viewToggle__qHn5e",viewBtn:"Bookings_viewBtn__halWp",active:"Bookings_active__EPiR9",filters:"Bookings_filters__efSCE",searchInput:"Bookings_searchInput__WU9rK",statusFilter:"Bookings_statusFilter__cEsIO",dateFilter:"Bookings_dateFilter__91_jc",bookingsContent:"Bookings_bookingsContent__UNoyE",bookingsList:"Bookings_bookingsList__1shaa",emptyState:"Bookings_emptyState__vwGqK",bookingCard:"Bookings_bookingCard__rr_2I",bookingHeader:"Bookings_bookingHeader__kN4o8",customerInfo:"Bookings_customerInfo__sr86g",statusBadge:"Bookings_statusBadge__YyBWn",statusPending:"Bookings_statusPending__KxyNp",statusConfirmed:"Bookings_statusConfirmed__9YBLQ",statusCompleted:"Bookings_statusCompleted___ZbBM",statusCancelled:"Bookings_statusCancelled__L3mcN",statusDefault:"Bookings_statusDefault__P9Wkr",bookingDetails:"Bookings_bookingDetails__uGdps",serviceInfo:"Bookings_serviceInfo__aPhqp",timeInfo:"Bookings_timeInfo__fzguw",bookingNotes:"Bookings_bookingNotes__y0sB6",bookingActions:"Bookings_bookingActions__GQrrp",statusSelect:"Bookings_statusSelect__1MW5p",calendarView:"Bookings_calendarView__mzDJ0",comingSoon:"Bookings_comingSoon__Ssaev",loadingContainer:"Bookings_loadingContainer__aLt2y",loadingSpinner:"Bookings_loadingSpinner__V78_d",spin:"Bookings_spin__RJm_5"}},2671:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{config:()=>k,default:()=>m,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>g,reportWebVitals:()=>v,routeModule:()=>b,unstable_getServerProps:()=>B,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>p,unstable_getStaticProps:()=>x});var n=t(7093),i=t(5244),o=t(1323),r=t(2899),l=t.n(r),c=t(6814),d=t(9562),_=e([c,d]);[c,d]=_.then?(await _)():_;let m=(0,o.l)(d,"default"),g=(0,o.l)(d,"getStaticProps"),u=(0,o.l)(d,"getStaticPaths"),h=(0,o.l)(d,"getServerSideProps"),k=(0,o.l)(d,"config"),v=(0,o.l)(d,"reportWebVitals"),x=(0,o.l)(d,"unstable_getStaticProps"),p=(0,o.l)(d,"unstable_getStaticPaths"),j=(0,o.l)(d,"unstable_getStaticParams"),B=(0,o.l)(d,"unstable_getServerProps"),N=(0,o.l)(d,"unstable_getServerSideProps"),b=new n.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/bookings",pathname:"/admin/bookings",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});a()}catch(e){a(e)}})},892:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(997),n=t(6689),i=t(8461),o=t.n(i);function r({bookings:e}){let[s,t]=(0,n.useState)(null),[i,r]=(0,n.useState)("30d"),l=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),c=e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return s?(0,a.jsxs)("div",{className:o().analyticsContainer,children:[(0,a.jsxs)("div",{className:o().analyticsHeader,children:[a.jsx("h3",{children:"Booking Analytics"}),(0,a.jsxs)("div",{className:o().timeRangeSelector,children:[a.jsx("button",{className:"7d"===i?o().active:"",onClick:()=>r("7d"),children:"7 Days"}),a.jsx("button",{className:"30d"===i?o().active:"",onClick:()=>r("30d"),children:"30 Days"}),a.jsx("button",{className:"90d"===i?o().active:"",onClick:()=>r("90d"),children:"90 Days"}),a.jsx("button",{className:"1y"===i?o().active:"",onClick:()=>r("1y"),children:"1 Year"})]})]}),(0,a.jsxs)("div",{className:o().metricsGrid,children:[(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:s.totalBookings}),a.jsx("div",{className:o().metricLabel,children:"Total Bookings"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:l(s.totalRevenue)}),a.jsx("div",{className:o().metricLabel,children:"Total Revenue"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:l(s.averageBookingValue)}),a.jsx("div",{className:o().metricLabel,children:"Avg Booking Value"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:s.bookingsThisMonth}),a.jsx("div",{className:o().metricLabel,children:"This Month"})]})]}),(0,a.jsxs)("div",{className:o().chartsGrid,children:[(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h4",{children:"Booking Status"}),a.jsx("div",{className:o().statusChart,children:Object.entries(s.statusBreakdown).map(([e,s])=>(0,a.jsxs)("div",{className:o().statusItem,children:[a.jsx("div",{className:o().statusIndicator,style:{backgroundColor:c(e)}}),a.jsx("span",{className:o().statusLabel,children:e}),a.jsx("span",{className:o().statusCount,children:s})]},e))})]}),(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h4",{children:"Top Services"}),a.jsx("div",{className:o().topList,children:s.topServices.map((e,s)=>(0,a.jsxs)("div",{className:o().topItem,children:[(0,a.jsxs)("div",{className:o().topRank,children:["#",s+1]}),(0,a.jsxs)("div",{className:o().topInfo,children:[a.jsx("div",{className:o().topName,children:e.name}),(0,a.jsxs)("div",{className:o().topStats,children:[e.count," bookings • ",l(e.revenue)]})]})]},e.name))})]}),(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h4",{children:"Top Artists"}),a.jsx("div",{className:o().topList,children:s.topArtists.map((e,s)=>(0,a.jsxs)("div",{className:o().topItem,children:[(0,a.jsxs)("div",{className:o().topRank,children:["#",s+1]}),(0,a.jsxs)("div",{className:o().topInfo,children:[a.jsx("div",{className:o().topName,children:e.name}),(0,a.jsxs)("div",{className:o().topStats,children:[e.count," bookings • ",l(e.revenue)]})]})]},e.name))})]}),(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h4",{children:"Monthly Trend"}),a.jsx("div",{className:o().trendChart,children:s.monthlyTrend.map((e,s)=>(0,a.jsxs)("div",{className:o().trendItem,children:[a.jsx("div",{className:o().trendMonth,children:e.month}),(0,a.jsxs)("div",{className:o().trendBookings,children:[e.bookings," bookings"]}),a.jsx("div",{className:o().trendRevenue,children:l(e.revenue)})]},s))})]})]})]}):a.jsx("div",{className:o().analyticsContainer,children:a.jsx("div",{className:o().emptyState,children:a.jsx("p",{children:"No booking data available for analysis"})})})}},3337:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(997),n=t(6689),i=t(9113),o=t.n(i);function r({bookings:e,onBookingClick:s,onDateClick:t}){let[i,r]=(0,n.useState)(new Date),[l,c]=(0,n.useState)([]),[d,_]=(0,n.useState)({}),m=e=>{let s=new Date(i);"prev"===e?s.setMonth(s.getMonth()-1):s.setMonth(s.getMonth()+1),r(s)},g=e=>{let s=new Date;return e.toDateString()===s.toDateString()},u=e=>e.getMonth()===i.getMonth(),h=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),k=e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return(0,a.jsxs)("div",{className:o().calendarContainer,children:[(0,a.jsxs)("div",{className:o().calendarHeader,children:[(0,a.jsxs)("div",{className:o().monthNavigation,children:[a.jsx("button",{onClick:()=>m("prev"),className:o().navButton,children:"←"}),(0,a.jsxs)("h2",{className:o().monthTitle,children:[["January","February","March","April","May","June","July","August","September","October","November","December"][i.getMonth()]," ",i.getFullYear()]}),a.jsx("button",{onClick:()=>m("next"),className:o().navButton,children:"→"})]}),a.jsx("button",{onClick:()=>{r(new Date)},className:o().todayButton,children:"Today"})]}),(0,a.jsxs)("div",{className:o().calendar,children:[a.jsx("div",{className:o().dayHeaders,children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>a.jsx("div",{className:o().dayHeader,children:e},e))}),a.jsx("div",{className:o().calendarGrid,children:l.map((e,n)=>{let i=d[e.toISOString().split("T")[0]]||[];return(0,a.jsxs)("div",{className:`${o().calendarDay} ${g(e)?o().today:""} ${u(e)?"":o().otherMonth}`,onClick:()=>t?.(e),children:[a.jsx("div",{className:o().dayNumber,children:e.getDate()}),(0,a.jsxs)("div",{className:o().bookingsContainer,children:[i.slice(0,3).map(e=>(0,a.jsxs)("div",{className:o().bookingItem,style:{borderLeftColor:k(e.status)},onClick:t=>{t.stopPropagation(),s?.(e)},title:`${e.customer_name} - ${e.service_name} at ${h(e.start_time)}`,children:[a.jsx("div",{className:o().bookingTime,children:h(e.start_time)}),a.jsx("div",{className:o().bookingCustomer,children:e.customer_name}),a.jsx("div",{className:o().bookingService,children:e.service_name})]},e.id)),i.length>3&&(0,a.jsxs)("div",{className:o().moreBookings,children:["+",i.length-3," more"]})]})]},n)})})]})]})}},9562:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>p});var n=t(997),i=t(6689),o=t(968),r=t.n(o),l=t(1163),c=t(1664),d=t.n(c),_=t(4845),m=t(3337),g=t(892),u=t(8568),h=t(3590),k=t(8704),v=t.n(k),x=e([_,h]);function p(){let e=(0,l.useRouter)(),{user:s,loading:t}=(0,u.a)(),[a,o]=(0,i.useState)([]),[c,k]=(0,i.useState)([]),[x,p]=(0,i.useState)(!0),[j,B]=(0,i.useState)(new Date().toISOString().split("T")[0]),[N,b]=(0,i.useState)("all"),[C,y]=(0,i.useState)(""),[S,f]=(0,i.useState)("list"),A=async(e,s)=>{try{let t=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({status:s})})).ok)throw Error("Failed to update booking status");o(t=>t.map(t=>t.id===e?{...t,status:s}:t))}catch(e){console.error("Error updating booking status:",e)}},w=e=>{switch(e){case"confirmed":return v().statusConfirmed;case"pending":return v().statusPending;case"completed":return v().statusCompleted;case"cancelled":return v().statusCancelled;default:return v().statusDefault}},I=e=>{let s=new Date(e);return{date:s.toLocaleDateString(),time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}};return t||x?n.jsx(_.Z,{children:(0,n.jsxs)("div",{className:v().loadingContainer,children:[n.jsx("div",{className:v().loadingSpinner}),n.jsx("p",{children:"Loading bookings..."})]})}):(0,n.jsxs)(_.Z,{children:[(0,n.jsxs)(r(),{children:[n.jsx("title",{children:"Bookings Management | Ocean Soul Sparkles Admin"}),n.jsx("meta",{name:"description",content:"Manage customer bookings and appointments"})]}),(0,n.jsxs)("div",{className:v().bookingsContainer,children:[(0,n.jsxs)("header",{className:v().header,children:[n.jsx("h1",{className:v().title,children:"Bookings Management"}),(0,n.jsxs)("div",{className:v().headerActions,children:[n.jsx(d(),{href:"/admin/bookings/new",className:v().newBookingBtn,children:"+ New Booking"}),n.jsx("button",{className:v().backButton,onClick:()=>e.push("/"),children:"← Back to Dashboard"})]})]}),n.jsx(g.Z,{bookings:a}),(0,n.jsxs)("div",{className:v().controlsPanel,children:[(0,n.jsxs)("div",{className:v().viewToggle,children:[n.jsx("button",{className:`${v().viewBtn} ${"list"===S?v().active:""}`,onClick:()=>f("list"),children:"List View"}),n.jsx("button",{className:`${v().viewBtn} ${"calendar"===S?v().active:""}`,onClick:()=>f("calendar"),children:"Calendar View"})]}),(0,n.jsxs)("div",{className:v().filters,children:[n.jsx("input",{type:"text",placeholder:"Search bookings...",value:C,onChange:e=>y(e.target.value),className:v().searchInput}),(0,n.jsxs)("select",{value:N,onChange:e=>b(e.target.value),className:v().statusFilter,children:[n.jsx("option",{value:"all",children:"All Status"}),n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]}),"list"===S&&n.jsx("input",{type:"date",value:j,onChange:e=>B(e.target.value),className:v().dateFilter})]})]}),(0,n.jsxs)("div",{className:v().bookingsContent,children:["list"===S?n.jsx("div",{className:v().bookingsList,children:0===c.length?n.jsx("div",{className:v().emptyState,children:n.jsx("p",{children:"No bookings found for the selected criteria."})}):c.map(e=>{let{date:s,time:t}=I(e.start_time),a=I(e.end_time).time;return(0,n.jsxs)("div",{className:v().bookingCard,children:[(0,n.jsxs)("div",{className:v().bookingHeader,children:[(0,n.jsxs)("div",{className:v().customerInfo,children:[n.jsx("h3",{children:e.customer_name}),n.jsx("p",{children:e.customer_email})]}),n.jsx("div",{className:`${v().statusBadge} ${w(e.status)}`,children:e.status})]}),(0,n.jsxs)("div",{className:v().bookingDetails,children:[(0,n.jsxs)("div",{className:v().serviceInfo,children:[n.jsx("strong",{children:e.service_name}),(0,n.jsxs)("p",{children:["Artist: ",e.artist]}),(0,n.jsxs)("p",{children:["Price: $",e.price]})]}),(0,n.jsxs)("div",{className:v().timeInfo,children:[n.jsx("p",{children:n.jsx("strong",{children:s})}),(0,n.jsxs)("p",{children:[t," - ",a]})]})]}),e.notes&&(0,n.jsxs)("div",{className:v().bookingNotes,children:[n.jsx("strong",{children:"Notes:"})," ",e.notes]}),(0,n.jsxs)("div",{className:v().bookingActions,children:[n.jsx(d(),{href:`/admin/bookings/${e.id}`,className:v().viewBtn,children:"View Details"}),(0,n.jsxs)("select",{value:e.status,onChange:s=>A(e.id,s.target.value),className:v().statusSelect,children:[n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]},e.id)})}):n.jsx("div",{className:v().calendarView,children:n.jsx(m.Z,{bookings:a,onBookingClick:s=>{e.push(`/admin/bookings/${s.id}`)},onDateClick:e=>{let s=e.toISOString().split("T")[0];B(s),f("list"),h.toast.info(`Switched to list view for ${e.toLocaleDateString()}`)}})}),"        "]})]})]})}[_,h]=x.then?(await x)():x,a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[2899,6212,1664,7441],()=>t(2671));module.exports=a})();