"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FulfillmentShipmentDetails = void 0;
const core = __importStar(require("../../core"));
const FulfillmentRecipient_1 = require("./FulfillmentRecipient");
exports.FulfillmentShipmentDetails = core.serialization.object({
    recipient: FulfillmentRecipient_1.FulfillmentRecipient.optional(),
    carrier: core.serialization.string().optionalNullable(),
    shippingNote: core.serialization.property("shipping_note", core.serialization.string().optionalNullable()),
    shippingType: core.serialization.property("shipping_type", core.serialization.string().optionalNullable()),
    trackingNumber: core.serialization.property("tracking_number", core.serialization.string().optionalNullable()),
    trackingUrl: core.serialization.property("tracking_url", core.serialization.string().optionalNullable()),
    placedAt: core.serialization.property("placed_at", core.serialization.string().optional()),
    inProgressAt: core.serialization.property("in_progress_at", core.serialization.string().optional()),
    packagedAt: core.serialization.property("packaged_at", core.serialization.string().optional()),
    expectedShippedAt: core.serialization.property("expected_shipped_at", core.serialization.string().optionalNullable()),
    shippedAt: core.serialization.property("shipped_at", core.serialization.string().optional()),
    canceledAt: core.serialization.property("canceled_at", core.serialization.string().optionalNullable()),
    cancelReason: core.serialization.property("cancel_reason", core.serialization.string().optionalNullable()),
    failedAt: core.serialization.property("failed_at", core.serialization.string().optional()),
    failureReason: core.serialization.property("failure_reason", core.serialization.string().optionalNullable()),
});
