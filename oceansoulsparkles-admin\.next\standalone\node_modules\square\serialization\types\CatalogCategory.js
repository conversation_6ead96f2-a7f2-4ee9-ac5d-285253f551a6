"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogCategory = void 0;
const serializers = __importStar(require("../index"));
const core = __importStar(require("../../core"));
const CatalogCategoryType_1 = require("./CatalogCategoryType");
const CatalogEcomSeoData_1 = require("./CatalogEcomSeoData");
const CategoryPathToRootNode_1 = require("./CategoryPathToRootNode");
exports.CatalogCategory = core.serialization.object({
    name: core.serialization.string().optionalNullable(),
    imageIds: core.serialization.property("image_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    categoryType: core.serialization.property("category_type", CatalogCategoryType_1.CatalogCategoryType.optional()),
    parentCategory: core.serialization.property("parent_category", core.serialization.lazyObject(() => serializers.CatalogObjectCategory).optional()),
    isTopLevel: core.serialization.property("is_top_level", core.serialization.boolean().optionalNullable()),
    channels: core.serialization.list(core.serialization.string()).optionalNullable(),
    availabilityPeriodIds: core.serialization.property("availability_period_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    onlineVisibility: core.serialization.property("online_visibility", core.serialization.boolean().optionalNullable()),
    rootCategory: core.serialization.property("root_category", core.serialization.string().optional()),
    ecomSeoData: core.serialization.property("ecom_seo_data", CatalogEcomSeoData_1.CatalogEcomSeoData.optional()),
    pathToRoot: core.serialization.property("path_to_root", core.serialization.list(CategoryPathToRootNode_1.CategoryPathToRootNode).optionalNullable()),
});
