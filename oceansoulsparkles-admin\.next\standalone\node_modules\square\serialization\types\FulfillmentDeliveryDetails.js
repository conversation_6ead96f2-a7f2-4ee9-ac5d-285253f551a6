"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FulfillmentDeliveryDetails = void 0;
const core = __importStar(require("../../core"));
const FulfillmentRecipient_1 = require("./FulfillmentRecipient");
const FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType_1 = require("./FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType");
exports.FulfillmentDeliveryDetails = core.serialization.object({
    recipient: FulfillmentRecipient_1.FulfillmentRecipient.optional(),
    scheduleType: core.serialization.property("schedule_type", FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType_1.FulfillmentDeliveryDetailsOrderFulfillmentDeliveryDetailsScheduleType.optional()),
    placedAt: core.serialization.property("placed_at", core.serialization.string().optional()),
    deliverAt: core.serialization.property("deliver_at", core.serialization.string().optionalNullable()),
    prepTimeDuration: core.serialization.property("prep_time_duration", core.serialization.string().optionalNullable()),
    deliveryWindowDuration: core.serialization.property("delivery_window_duration", core.serialization.string().optionalNullable()),
    note: core.serialization.string().optionalNullable(),
    completedAt: core.serialization.property("completed_at", core.serialization.string().optionalNullable()),
    inProgressAt: core.serialization.property("in_progress_at", core.serialization.string().optional()),
    rejectedAt: core.serialization.property("rejected_at", core.serialization.string().optional()),
    readyAt: core.serialization.property("ready_at", core.serialization.string().optional()),
    deliveredAt: core.serialization.property("delivered_at", core.serialization.string().optional()),
    canceledAt: core.serialization.property("canceled_at", core.serialization.string().optional()),
    cancelReason: core.serialization.property("cancel_reason", core.serialization.string().optionalNullable()),
    courierPickupAt: core.serialization.property("courier_pickup_at", core.serialization.string().optionalNullable()),
    courierPickupWindowDuration: core.serialization.property("courier_pickup_window_duration", core.serialization.string().optionalNullable()),
    isNoContactDelivery: core.serialization.property("is_no_contact_delivery", core.serialization.boolean().optionalNullable()),
    dropoffNotes: core.serialization.property("dropoff_notes", core.serialization.string().optionalNullable()),
    courierProviderName: core.serialization.property("courier_provider_name", core.serialization.string().optionalNullable()),
    courierSupportPhoneNumber: core.serialization.property("courier_support_phone_number", core.serialization.string().optionalNullable()),
    squareDeliveryId: core.serialization.property("square_delivery_id", core.serialization.string().optionalNullable()),
    externalDeliveryId: core.serialization.property("external_delivery_id", core.serialization.string().optionalNullable()),
    managedDelivery: core.serialization.property("managed_delivery", core.serialization.boolean().optionalNullable()),
});
