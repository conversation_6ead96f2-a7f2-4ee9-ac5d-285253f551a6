/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/mobile-test",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/AdminLayout.module.css":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/AdminLayout.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Admin Layout Styles */\\r\\n.AdminLayout_adminLayout__5Oi4c {\\r\\n  display: flex;\\r\\n  min-height: 100vh;\\r\\n  background: var(--admin-bg-secondary);\\r\\n}\\r\\n\\r\\n.AdminLayout_mainContent__INtLu {\\r\\n  flex: 1 1;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  margin-left: 280px;\\r\\n  transition: margin-left var(--admin-transition-normal);\\r\\n}\\r\\n\\r\\n.AdminLayout_mainContent__INtLu.AdminLayout_sidebarCollapsed__oAEhD {\\r\\n  margin-left: 70px;\\r\\n}\\r\\n\\r\\n.AdminLayout_pageContent__aWMEk {\\r\\n  flex: 1 1;\\r\\n  padding: var(--admin-spacing-lg);\\r\\n  overflow-y: auto;\\r\\n}\\r\\n\\r\\n.AdminLayout_adminFooter__mTvA1 {\\r\\n  background: var(--admin-bg-primary);\\r\\n  border-top: 1px solid var(--admin-border-light);\\r\\n  padding: var(--admin-spacing-md) var(--admin-spacing-lg);\\r\\n  margin-top: auto;\\r\\n}\\r\\n\\r\\n.AdminLayout_footerContent__z6du0 {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.AdminLayout_footerLeft__gGY8P {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: var(--admin-spacing-md);\\r\\n  color: var(--admin-gray);\\r\\n  font-size: 0.85rem;\\r\\n}\\r\\n\\r\\n.AdminLayout_version__vpU9q {\\r\\n  background: var(--admin-bg-tertiary);\\r\\n  padding: 2px 8px;\\r\\n  border-radius: var(--admin-radius-sm);\\r\\n  font-weight: 600;\\r\\n  font-size: 0.75rem;\\r\\n}\\r\\n\\r\\n.AdminLayout_footerRight__kyodA {\\r\\n  display: flex;\\r\\n  gap: var(--admin-spacing-lg);\\r\\n}\\r\\n\\r\\n.AdminLayout_footerLink__jvWuv {\\r\\n  color: var(--admin-gray);\\r\\n  text-decoration: none;\\r\\n  font-size: 0.85rem;\\r\\n  transition: color var(--admin-transition-normal);\\r\\n}\\r\\n\\r\\n.AdminLayout_footerLink__jvWuv:hover {\\r\\n  color: var(--admin-primary);\\r\\n}\\r\\n\\r\\n.AdminLayout_mobileOverlay__BNO2v {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  z-index: var(--admin-z-modal-backdrop);\\r\\n}\\r\\n\\r\\n.AdminLayout_securityBanner__KTGT5 {\\r\\n  position: fixed;\\r\\n  bottom: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  background: var(--admin-primary);\\r\\n  color: white;\\r\\n  padding: var(--admin-spacing-xs) var(--admin-spacing-md);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: var(--admin-spacing-sm);\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n  z-index: var(--admin-z-fixed);\\r\\n}\\r\\n\\r\\n.AdminLayout_securityIcon__eZwIM {\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n.AdminLayout_loadingContainer__Wbedv {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 100vh;\\r\\n  background: var(--admin-bg-secondary);\\r\\n  color: var(--admin-gray);\\r\\n}\\r\\n\\r\\n.AdminLayout_loadingSpinner__C8mvO {\\r\\n  width: 40px;\\r\\n  height: 40px;\\r\\n  border: 4px solid var(--admin-border-light);\\r\\n  border-top: 4px solid var(--admin-primary);\\r\\n  border-radius: 50%;\\r\\n  animation: AdminLayout_spin__DZv4U 1s linear infinite;\\r\\n  margin-bottom: var(--admin-spacing-md);\\r\\n}\\r\\n\\r\\n@keyframes AdminLayout_spin__DZv4U {\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile Responsive */\\r\\n@media (max-width: 768px) {\\r\\n  .AdminLayout_adminLayout__5Oi4c {\\r\\n    display: flex;\\r\\n    flex-direction: column;\\r\\n    min-height: 100vh;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .AdminLayout_mainContent__INtLu {\\r\\n    flex: 1 1;\\r\\n    margin-left: 0 !important;\\r\\n    padding-bottom: 80px; /* Account for bottom navigation */\\r\\n    width: 100%;\\r\\n    max-width: 100vw;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .AdminLayout_mainContent__INtLu.AdminLayout_sidebarCollapsed__oAEhD {\\r\\n    margin-left: 0 !important;\\r\\n  }\\r\\n\\r\\n  .AdminLayout_pageContent__aWMEk {\\r\\n    padding: var(--admin-spacing-md);\\r\\n    padding-bottom: var(--admin-spacing-lg); /* Extra padding for bottom nav */\\r\\n    max-width: 100%;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .AdminLayout_footerContent__z6du0 {\\r\\n    flex-direction: column;\\r\\n    gap: var(--admin-spacing-sm);\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  .AdminLayout_footerRight__kyodA {\\r\\n    gap: var(--admin-spacing-md);\\r\\n  }\\r\\n\\r\\n  .AdminLayout_securityBanner__KTGT5 {\\r\\n    position: relative;\\r\\n    margin-top: var(--admin-spacing-md);\\r\\n    margin-bottom: 80px; /* Account for bottom navigation */\\r\\n  }\\r\\n\\r\\n  /* Hide desktop sidebar on mobile */\\r\\n  .AdminLayout_adminLayout__5Oi4c .AdminLayout_sidebar__1zyFe {\\r\\n    display: none !important;\\r\\n  }\\r\\n\\r\\n  /* Ensure all content respects mobile viewport */\\r\\n  .AdminLayout_adminLayout__5Oi4c *,\\r\\n  .AdminLayout_mainContent__INtLu *,\\r\\n  .AdminLayout_pageContent__aWMEk * {\\r\\n    max-width: 100%;\\r\\n    box-sizing: border-box;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile Bottom Navigation */\\r\\n.AdminLayout_mobileBottomNav__2kopO {\\r\\n  display: none; /* Hidden on desktop */\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .AdminLayout_mobileBottomNav__2kopO {\\r\\n    display: block !important;\\r\\n    position: fixed;\\r\\n    bottom: 0;\\r\\n    left: 0;\\r\\n    right: 0;\\r\\n    z-index: 1000;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile overlay for hamburger menu */\\r\\n.AdminLayout_mobileOverlay__BNO2v {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  z-index: 999;\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .AdminLayout_mobileOverlay__BNO2v {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .AdminLayout_pageContent__aWMEk {\\r\\n    padding: var(--admin-spacing-sm);\\r\\n  }\\r\\n\\r\\n  .AdminLayout_footerLeft__gGY8P {\\r\\n    flex-direction: column;\\r\\n    gap: var(--admin-spacing-xs);\\r\\n  }\\r\\n\\r\\n  .AdminLayout_securityBanner__KTGT5 {\\r\\n    font-size: 0.75rem;\\r\\n    padding: var(--admin-spacing-xs);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Debug styles for mobile development */\\r\\n.AdminLayout_debugMobile__hcFrB {\\r\\n  position: fixed;\\r\\n  top: 10px;\\r\\n  right: 10px;\\r\\n  background: rgba(255, 0, 0, 0.8);\\r\\n  color: white;\\r\\n  padding: 5px 10px;\\r\\n  border-radius: 4px;\\r\\n  font-size: 12px;\\r\\n  z-index: 9999;\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .AdminLayout_debugMobile__hcFrB {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/admin/AdminLayout.module.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,aAAa;EACb,iBAAiB;EACjB,qCAAqC;AACvC;;AAEA;EACE,SAAO;EACP,aAAa;EACb,sBAAsB;EACtB,kBAAkB;EAClB,sDAAsD;AACxD;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,SAAO;EACP,gCAAgC;EAChC,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,+CAA+C;EAC/C,wDAAwD;EACxD,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,iBAAiB;EACjB,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,4BAA4B;EAC5B,wBAAwB;EACxB,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,gBAAgB;EAChB,qCAAqC;EACrC,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,4BAA4B;AAC9B;;AAEA;EACE,wBAAwB;EACxB,qBAAqB;EACrB,kBAAkB;EAClB,gDAAgD;AAClD;;AAEA;EACE,2BAA2B;AAC7B;;AAEA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,8BAA8B;EAC9B,sCAAsC;AACxC;;AAEA;EACE,eAAe;EACf,SAAS;EACT,OAAO;EACP,QAAQ;EACR,gCAAgC;EAChC,YAAY;EACZ,wDAAwD;EACxD,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,4BAA4B;EAC5B,iBAAiB;EACjB,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,iBAAiB;EACjB,qCAAqC;EACrC,wBAAwB;AAC1B;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,2CAA2C;EAC3C,0CAA0C;EAC1C,kBAAkB;EAClB,qDAAkC;EAClC,sCAAsC;AACxC;;AAEA;EACE;IACE,yBAAyB;EAC3B;AACF;;AAEA,sBAAsB;AACtB;EACE;IACE,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,kBAAkB;EACpB;;EAEA;IACE,SAAO;IACP,yBAAyB;IACzB,oBAAoB,EAAE,kCAAkC;IACxD,WAAW;IACX,gBAAgB;IAChB,kBAAkB;EACpB;;EAEA;IACE,yBAAyB;EAC3B;;EAEA;IACE,gCAAgC;IAChC,uCAAuC,EAAE,iCAAiC;IAC1E,eAAe;IACf,kBAAkB;EACpB;;EAEA;IACE,sBAAsB;IACtB,4BAA4B;IAC5B,kBAAkB;EACpB;;EAEA;IACE,4BAA4B;EAC9B;;EAEA;IACE,kBAAkB;IAClB,mCAAmC;IACnC,mBAAmB,EAAE,kCAAkC;EACzD;;EAEA,mCAAmC;EACnC;IACE,wBAAwB;EAC1B;;EAEA,gDAAgD;EAChD;;;IAGE,eAAe;IACf,sBAAsB;EACxB;AACF;;AAEA,6BAA6B;AAC7B;EACE,aAAa,EAAE,sBAAsB;AACvC;;AAEA;EACE;IACE,yBAAyB;IACzB,eAAe;IACf,SAAS;IACT,OAAO;IACP,QAAQ;IACR,aAAa;EACf;AACF;;AAEA,sCAAsC;AACtC;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,8BAA8B;EAC9B,YAAY;EACZ,aAAa;AACf;;AAEA;EACE;IACE,cAAc;EAChB;AACF;;AAEA;EACE;IACE,gCAAgC;EAClC;;EAEA;IACE,sBAAsB;IACtB,4BAA4B;EAC9B;;EAEA;IACE,kBAAkB;IAClB,gCAAgC;EAClC;AACF;;AAEA,wCAAwC;AACxC;EACE,eAAe;EACf,SAAS;EACT,WAAW;EACX,gCAAgC;EAChC,YAAY;EACZ,iBAAiB;EACjB,kBAAkB;EAClB,eAAe;EACf,aAAa;EACb,aAAa;AACf;;AAEA;EACE;IACE,cAAc;EAChB;AACF\",\"sourcesContent\":[\"/* Admin Layout Styles */\\r\\n.adminLayout {\\r\\n  display: flex;\\r\\n  min-height: 100vh;\\r\\n  background: var(--admin-bg-secondary);\\r\\n}\\r\\n\\r\\n.mainContent {\\r\\n  flex: 1;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  margin-left: 280px;\\r\\n  transition: margin-left var(--admin-transition-normal);\\r\\n}\\r\\n\\r\\n.mainContent.sidebarCollapsed {\\r\\n  margin-left: 70px;\\r\\n}\\r\\n\\r\\n.pageContent {\\r\\n  flex: 1;\\r\\n  padding: var(--admin-spacing-lg);\\r\\n  overflow-y: auto;\\r\\n}\\r\\n\\r\\n.adminFooter {\\r\\n  background: var(--admin-bg-primary);\\r\\n  border-top: 1px solid var(--admin-border-light);\\r\\n  padding: var(--admin-spacing-md) var(--admin-spacing-lg);\\r\\n  margin-top: auto;\\r\\n}\\r\\n\\r\\n.footerContent {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.footerLeft {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: var(--admin-spacing-md);\\r\\n  color: var(--admin-gray);\\r\\n  font-size: 0.85rem;\\r\\n}\\r\\n\\r\\n.version {\\r\\n  background: var(--admin-bg-tertiary);\\r\\n  padding: 2px 8px;\\r\\n  border-radius: var(--admin-radius-sm);\\r\\n  font-weight: 600;\\r\\n  font-size: 0.75rem;\\r\\n}\\r\\n\\r\\n.footerRight {\\r\\n  display: flex;\\r\\n  gap: var(--admin-spacing-lg);\\r\\n}\\r\\n\\r\\n.footerLink {\\r\\n  color: var(--admin-gray);\\r\\n  text-decoration: none;\\r\\n  font-size: 0.85rem;\\r\\n  transition: color var(--admin-transition-normal);\\r\\n}\\r\\n\\r\\n.footerLink:hover {\\r\\n  color: var(--admin-primary);\\r\\n}\\r\\n\\r\\n.mobileOverlay {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  z-index: var(--admin-z-modal-backdrop);\\r\\n}\\r\\n\\r\\n.securityBanner {\\r\\n  position: fixed;\\r\\n  bottom: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  background: var(--admin-primary);\\r\\n  color: white;\\r\\n  padding: var(--admin-spacing-xs) var(--admin-spacing-md);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: var(--admin-spacing-sm);\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n  z-index: var(--admin-z-fixed);\\r\\n}\\r\\n\\r\\n.securityIcon {\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n.loadingContainer {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 100vh;\\r\\n  background: var(--admin-bg-secondary);\\r\\n  color: var(--admin-gray);\\r\\n}\\r\\n\\r\\n.loadingSpinner {\\r\\n  width: 40px;\\r\\n  height: 40px;\\r\\n  border: 4px solid var(--admin-border-light);\\r\\n  border-top: 4px solid var(--admin-primary);\\r\\n  border-radius: 50%;\\r\\n  animation: spin 1s linear infinite;\\r\\n  margin-bottom: var(--admin-spacing-md);\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile Responsive */\\r\\n@media (max-width: 768px) {\\r\\n  .adminLayout {\\r\\n    display: flex;\\r\\n    flex-direction: column;\\r\\n    min-height: 100vh;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .mainContent {\\r\\n    flex: 1;\\r\\n    margin-left: 0 !important;\\r\\n    padding-bottom: 80px; /* Account for bottom navigation */\\r\\n    width: 100%;\\r\\n    max-width: 100vw;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .mainContent.sidebarCollapsed {\\r\\n    margin-left: 0 !important;\\r\\n  }\\r\\n\\r\\n  .pageContent {\\r\\n    padding: var(--admin-spacing-md);\\r\\n    padding-bottom: var(--admin-spacing-lg); /* Extra padding for bottom nav */\\r\\n    max-width: 100%;\\r\\n    overflow-x: hidden;\\r\\n  }\\r\\n\\r\\n  .footerContent {\\r\\n    flex-direction: column;\\r\\n    gap: var(--admin-spacing-sm);\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  .footerRight {\\r\\n    gap: var(--admin-spacing-md);\\r\\n  }\\r\\n\\r\\n  .securityBanner {\\r\\n    position: relative;\\r\\n    margin-top: var(--admin-spacing-md);\\r\\n    margin-bottom: 80px; /* Account for bottom navigation */\\r\\n  }\\r\\n\\r\\n  /* Hide desktop sidebar on mobile */\\r\\n  .adminLayout .sidebar {\\r\\n    display: none !important;\\r\\n  }\\r\\n\\r\\n  /* Ensure all content respects mobile viewport */\\r\\n  .adminLayout *,\\r\\n  .mainContent *,\\r\\n  .pageContent * {\\r\\n    max-width: 100%;\\r\\n    box-sizing: border-box;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile Bottom Navigation */\\r\\n.mobileBottomNav {\\r\\n  display: none; /* Hidden on desktop */\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .mobileBottomNav {\\r\\n    display: block !important;\\r\\n    position: fixed;\\r\\n    bottom: 0;\\r\\n    left: 0;\\r\\n    right: 0;\\r\\n    z-index: 1000;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Mobile overlay for hamburger menu */\\r\\n.mobileOverlay {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  z-index: 999;\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .mobileOverlay {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .pageContent {\\r\\n    padding: var(--admin-spacing-sm);\\r\\n  }\\r\\n\\r\\n  .footerLeft {\\r\\n    flex-direction: column;\\r\\n    gap: var(--admin-spacing-xs);\\r\\n  }\\r\\n\\r\\n  .securityBanner {\\r\\n    font-size: 0.75rem;\\r\\n    padding: var(--admin-spacing-xs);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Debug styles for mobile development */\\r\\n.debugMobile {\\r\\n  position: fixed;\\r\\n  top: 10px;\\r\\n  right: 10px;\\r\\n  background: rgba(255, 0, 0, 0.8);\\r\\n  color: white;\\r\\n  padding: 5px 10px;\\r\\n  border-radius: 4px;\\r\\n  font-size: 12px;\\r\\n  z-index: 9999;\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .debugMobile {\\r\\n    display: block;\\r\\n  }\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"adminLayout\": \"AdminLayout_adminLayout__5Oi4c\",\n\t\"mainContent\": \"AdminLayout_mainContent__INtLu\",\n\t\"sidebarCollapsed\": \"AdminLayout_sidebarCollapsed__oAEhD\",\n\t\"pageContent\": \"AdminLayout_pageContent__aWMEk\",\n\t\"adminFooter\": \"AdminLayout_adminFooter__mTvA1\",\n\t\"footerContent\": \"AdminLayout_footerContent__z6du0\",\n\t\"footerLeft\": \"AdminLayout_footerLeft__gGY8P\",\n\t\"version\": \"AdminLayout_version__vpU9q\",\n\t\"footerRight\": \"AdminLayout_footerRight__kyodA\",\n\t\"footerLink\": \"AdminLayout_footerLink__jvWuv\",\n\t\"mobileOverlay\": \"AdminLayout_mobileOverlay__BNO2v\",\n\t\"securityBanner\": \"AdminLayout_securityBanner__KTGT5\",\n\t\"securityIcon\": \"AdminLayout_securityIcon__eZwIM\",\n\t\"loadingContainer\": \"AdminLayout_loadingContainer__Wbedv\",\n\t\"loadingSpinner\": \"AdminLayout_loadingSpinner__C8mvO\",\n\t\"spin\": \"AdminLayout_spin__DZv4U\",\n\t\"sidebar\": \"AdminLayout_sidebar__1zyFe\",\n\t\"mobileBottomNav\": \"AdminLayout_mobileBottomNav__2kopO\",\n\t\"debugMobile\": \"AdminLayout_debugMobile__hcFrB\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/AdminLayout.module.css\n"));

/***/ })

});