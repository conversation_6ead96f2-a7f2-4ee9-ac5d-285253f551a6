"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckoutOptions = void 0;
const core = __importStar(require("../../core"));
const CustomField_1 = require("./CustomField");
const AcceptedPaymentMethods_1 = require("./AcceptedPaymentMethods");
const Money_1 = require("./Money");
const ShippingFee_1 = require("./ShippingFee");
exports.CheckoutOptions = core.serialization.object({
    allowTipping: core.serialization.property("allow_tipping", core.serialization.boolean().optionalNullable()),
    customFields: core.serialization.property("custom_fields", core.serialization.list(CustomField_1.CustomField).optionalNullable()),
    subscriptionPlanId: core.serialization.property("subscription_plan_id", core.serialization.string().optionalNullable()),
    redirectUrl: core.serialization.property("redirect_url", core.serialization.string().optionalNullable()),
    merchantSupportEmail: core.serialization.property("merchant_support_email", core.serialization.string().optionalNullable()),
    askForShippingAddress: core.serialization.property("ask_for_shipping_address", core.serialization.boolean().optionalNullable()),
    acceptedPaymentMethods: core.serialization.property("accepted_payment_methods", AcceptedPaymentMethods_1.AcceptedPaymentMethods.optional()),
    appFeeMoney: core.serialization.property("app_fee_money", Money_1.Money.optional()),
    shippingFee: core.serialization.property("shipping_fee", ShippingFee_1.ShippingFee.optional()),
    enableCoupon: core.serialization.property("enable_coupon", core.serialization.boolean().optionalNullable()),
    enableLoyalty: core.serialization.property("enable_loyalty", core.serialization.boolean().optionalNullable()),
});
