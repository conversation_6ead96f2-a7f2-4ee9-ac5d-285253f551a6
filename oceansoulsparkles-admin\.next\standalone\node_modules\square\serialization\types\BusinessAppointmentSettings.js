"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessAppointmentSettings = void 0;
const core = __importStar(require("../../core"));
const BusinessAppointmentSettingsBookingLocationType_1 = require("./BusinessAppointmentSettingsBookingLocationType");
const BusinessAppointmentSettingsAlignmentTime_1 = require("./BusinessAppointmentSettingsAlignmentTime");
const BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType_1 = require("./BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType");
const Money_1 = require("./Money");
const BusinessAppointmentSettingsCancellationPolicy_1 = require("./BusinessAppointmentSettingsCancellationPolicy");
exports.BusinessAppointmentSettings = core.serialization.object({
    locationTypes: core.serialization.property("location_types", core.serialization.list(BusinessAppointmentSettingsBookingLocationType_1.BusinessAppointmentSettingsBookingLocationType).optionalNullable()),
    alignmentTime: core.serialization.property("alignment_time", BusinessAppointmentSettingsAlignmentTime_1.BusinessAppointmentSettingsAlignmentTime.optional()),
    minBookingLeadTimeSeconds: core.serialization.property("min_booking_lead_time_seconds", core.serialization.number().optionalNullable()),
    maxBookingLeadTimeSeconds: core.serialization.property("max_booking_lead_time_seconds", core.serialization.number().optionalNullable()),
    anyTeamMemberBookingEnabled: core.serialization.property("any_team_member_booking_enabled", core.serialization.boolean().optionalNullable()),
    multipleServiceBookingEnabled: core.serialization.property("multiple_service_booking_enabled", core.serialization.boolean().optionalNullable()),
    maxAppointmentsPerDayLimitType: core.serialization.property("max_appointments_per_day_limit_type", BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType_1.BusinessAppointmentSettingsMaxAppointmentsPerDayLimitType.optional()),
    maxAppointmentsPerDayLimit: core.serialization.property("max_appointments_per_day_limit", core.serialization.number().optionalNullable()),
    cancellationWindowSeconds: core.serialization.property("cancellation_window_seconds", core.serialization.number().optionalNullable()),
    cancellationFeeMoney: core.serialization.property("cancellation_fee_money", Money_1.Money.optional()),
    cancellationPolicy: core.serialization.property("cancellation_policy", BusinessAppointmentSettingsCancellationPolicy_1.BusinessAppointmentSettingsCancellationPolicy.optional()),
    cancellationPolicyText: core.serialization.property("cancellation_policy_text", core.serialization.string().optionalNullable()),
    skipBookingFlowStaffSelection: core.serialization.property("skip_booking_flow_staff_selection", core.serialization.boolean().optionalNullable()),
});
