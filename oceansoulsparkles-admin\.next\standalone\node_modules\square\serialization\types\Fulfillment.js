"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fulfillment = void 0;
const core = __importStar(require("../../core"));
const FulfillmentType_1 = require("./FulfillmentType");
const FulfillmentState_1 = require("./FulfillmentState");
const FulfillmentFulfillmentLineItemApplication_1 = require("./FulfillmentFulfillmentLineItemApplication");
const FulfillmentFulfillmentEntry_1 = require("./FulfillmentFulfillmentEntry");
const FulfillmentPickupDetails_1 = require("./FulfillmentPickupDetails");
const FulfillmentShipmentDetails_1 = require("./FulfillmentShipmentDetails");
const FulfillmentDeliveryDetails_1 = require("./FulfillmentDeliveryDetails");
exports.Fulfillment = core.serialization.object({
    uid: core.serialization.string().optionalNullable(),
    type: FulfillmentType_1.FulfillmentType.optional(),
    state: FulfillmentState_1.FulfillmentState.optional(),
    lineItemApplication: core.serialization.property("line_item_application", FulfillmentFulfillmentLineItemApplication_1.FulfillmentFulfillmentLineItemApplication.optional()),
    entries: core.serialization.list(FulfillmentFulfillmentEntry_1.FulfillmentFulfillmentEntry).optional(),
    metadata: core.serialization
        .record(core.serialization.string(), core.serialization.string().optionalNullable())
        .optionalNullable(),
    pickupDetails: core.serialization.property("pickup_details", FulfillmentPickupDetails_1.FulfillmentPickupDetails.optional()),
    shipmentDetails: core.serialization.property("shipment_details", FulfillmentShipmentDetails_1.FulfillmentShipmentDetails.optional()),
    deliveryDetails: core.serialization.property("delivery_details", FulfillmentDeliveryDetails_1.FulfillmentDeliveryDetails.optional()),
});
