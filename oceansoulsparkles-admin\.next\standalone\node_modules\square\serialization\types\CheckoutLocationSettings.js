"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckoutLocationSettings = void 0;
const core = __importStar(require("../../core"));
const CheckoutLocationSettingsPolicy_1 = require("./CheckoutLocationSettingsPolicy");
const CheckoutLocationSettingsBranding_1 = require("./CheckoutLocationSettingsBranding");
const CheckoutLocationSettingsTipping_1 = require("./CheckoutLocationSettingsTipping");
const CheckoutLocationSettingsCoupons_1 = require("./CheckoutLocationSettingsCoupons");
exports.CheckoutLocationSettings = core.serialization.object({
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    customerNotesEnabled: core.serialization.property("customer_notes_enabled", core.serialization.boolean().optionalNullable()),
    policies: core.serialization.list(CheckoutLocationSettingsPolicy_1.CheckoutLocationSettingsPolicy).optionalNullable(),
    branding: CheckoutLocationSettingsBranding_1.CheckoutLocationSettingsBranding.optional(),
    tipping: CheckoutLocationSettingsTipping_1.CheckoutLocationSettingsTipping.optional(),
    coupons: CheckoutLocationSettingsCoupons_1.CheckoutLocationSettingsCoupons.optional(),
    updatedAt: core.serialization.property("updated_at", core.serialization.string().optional()),
});
