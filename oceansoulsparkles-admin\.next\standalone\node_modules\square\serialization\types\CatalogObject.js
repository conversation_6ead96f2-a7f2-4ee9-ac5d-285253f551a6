"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogObject = void 0;
const serializers = __importStar(require("../index"));
const core = __importStar(require("../../core"));
const CatalogObjectImage_1 = require("./CatalogObjectImage");
const CatalogObjectItemVariation_1 = require("./CatalogObjectItemVariation");
const CatalogObjectTax_1 = require("./CatalogObjectTax");
const CatalogObjectDiscount_1 = require("./CatalogObjectDiscount");
const CatalogObjectModifier_1 = require("./CatalogObjectModifier");
const CatalogObjectPricingRule_1 = require("./CatalogObjectPricingRule");
const CatalogObjectProductSet_1 = require("./CatalogObjectProductSet");
const CatalogObjectTimePeriod_1 = require("./CatalogObjectTimePeriod");
const CatalogObjectMeasurementUnit_1 = require("./CatalogObjectMeasurementUnit");
const CatalogObjectSubscriptionPlanVariation_1 = require("./CatalogObjectSubscriptionPlanVariation");
const CatalogObjectItemOptionValue_1 = require("./CatalogObjectItemOptionValue");
const CatalogObjectCustomAttributeDefinition_1 = require("./CatalogObjectCustomAttributeDefinition");
const CatalogObjectQuickAmountsSettings_1 = require("./CatalogObjectQuickAmountsSettings");
const CatalogObjectAvailabilityPeriod_1 = require("./CatalogObjectAvailabilityPeriod");
exports.CatalogObject = core.serialization
    .union("type", {
    ITEM: core.serialization.lazyObject(() => serializers.CatalogObjectItem),
    IMAGE: CatalogObjectImage_1.CatalogObjectImage,
    CATEGORY: core.serialization.lazyObject(() => serializers.CatalogObjectCategory),
    ITEM_VARIATION: CatalogObjectItemVariation_1.CatalogObjectItemVariation,
    TAX: CatalogObjectTax_1.CatalogObjectTax,
    DISCOUNT: CatalogObjectDiscount_1.CatalogObjectDiscount,
    MODIFIER_LIST: core.serialization.lazyObject(() => serializers.CatalogObjectModifierList),
    MODIFIER: CatalogObjectModifier_1.CatalogObjectModifier,
    PRICING_RULE: CatalogObjectPricingRule_1.CatalogObjectPricingRule,
    PRODUCT_SET: CatalogObjectProductSet_1.CatalogObjectProductSet,
    TIME_PERIOD: CatalogObjectTimePeriod_1.CatalogObjectTimePeriod,
    MEASUREMENT_UNIT: CatalogObjectMeasurementUnit_1.CatalogObjectMeasurementUnit,
    SUBSCRIPTION_PLAN_VARIATION: CatalogObjectSubscriptionPlanVariation_1.CatalogObjectSubscriptionPlanVariation,
    ITEM_OPTION: core.serialization.lazyObject(() => serializers.CatalogObjectItemOption),
    ITEM_OPTION_VAL: CatalogObjectItemOptionValue_1.CatalogObjectItemOptionValue,
    CUSTOM_ATTRIBUTE_DEFINITION: CatalogObjectCustomAttributeDefinition_1.CatalogObjectCustomAttributeDefinition,
    QUICK_AMOUNTS_SETTINGS: CatalogObjectQuickAmountsSettings_1.CatalogObjectQuickAmountsSettings,
    SUBSCRIPTION_PLAN: core.serialization.lazyObject(() => serializers.CatalogObjectSubscriptionPlan),
    AVAILABILITY_PERIOD: CatalogObjectAvailabilityPeriod_1.CatalogObjectAvailabilityPeriod,
})
    .transform({
    transform: (value) => value,
    untransform: (value) => value,
});
