"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogModifierList = void 0;
const serializers = __importStar(require("../index"));
const core = __importStar(require("../../core"));
const CatalogModifierListSelectionType_1 = require("./CatalogModifierListSelectionType");
const CatalogModifierListModifierType_1 = require("./CatalogModifierListModifierType");
exports.CatalogModifierList = core.serialization.object({
    name: core.serialization.string().optionalNullable(),
    ordinal: core.serialization.number().optionalNullable(),
    selectionType: core.serialization.property("selection_type", CatalogModifierListSelectionType_1.CatalogModifierListSelectionType.optional()),
    modifiers: core.serialization.list(core.serialization.lazy(() => serializers.CatalogObject)).optionalNullable(),
    imageIds: core.serialization.property("image_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    allowQuantities: core.serialization.property("allow_quantities", core.serialization.boolean().optionalNullable()),
    isConversational: core.serialization.property("is_conversational", core.serialization.boolean().optionalNullable()),
    modifierType: core.serialization.property("modifier_type", CatalogModifierListModifierType_1.CatalogModifierListModifierType.optional()),
    maxLength: core.serialization.property("max_length", core.serialization.number().optionalNullable()),
    textRequired: core.serialization.property("text_required", core.serialization.boolean().optionalNullable()),
    internalName: core.serialization.property("internal_name", core.serialization.string().optionalNullable()),
    minSelectedModifiers: core.serialization.property("min_selected_modifiers", core.serialization.bigint().optionalNullable()),
    maxSelectedModifiers: core.serialization.property("max_selected_modifiers", core.serialization.bigint().optionalNullable()),
    hiddenFromCustomer: core.serialization.property("hidden_from_customer", core.serialization.boolean().optionalNullable()),
});
