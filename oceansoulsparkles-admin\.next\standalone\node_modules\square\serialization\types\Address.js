"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Address = void 0;
const core = __importStar(require("../../core"));
const Country_1 = require("./Country");
exports.Address = core.serialization.object({
    addressLine1: core.serialization.property("address_line_1", core.serialization.string().optionalNullable()),
    addressLine2: core.serialization.property("address_line_2", core.serialization.string().optionalNullable()),
    addressLine3: core.serialization.property("address_line_3", core.serialization.string().optionalNullable()),
    locality: core.serialization.string().optionalNullable(),
    sublocality: core.serialization.string().optionalNullable(),
    sublocality2: core.serialization.property("sublocality_2", core.serialization.string().optionalNullable()),
    sublocality3: core.serialization.property("sublocality_3", core.serialization.string().optionalNullable()),
    administrativeDistrictLevel1: core.serialization.property("administrative_district_level_1", core.serialization.string().optionalNullable()),
    administrativeDistrictLevel2: core.serialization.property("administrative_district_level_2", core.serialization.string().optionalNullable()),
    administrativeDistrictLevel3: core.serialization.property("administrative_district_level_3", core.serialization.string().optionalNullable()),
    postalCode: core.serialization.property("postal_code", core.serialization.string().optionalNullable()),
    country: Country_1.Country.optional(),
    firstName: core.serialization.property("first_name", core.serialization.string().optionalNullable()),
    lastName: core.serialization.property("last_name", core.serialization.string().optionalNullable()),
});
