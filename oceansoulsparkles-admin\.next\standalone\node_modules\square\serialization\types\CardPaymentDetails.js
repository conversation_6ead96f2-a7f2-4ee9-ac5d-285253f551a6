"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardPaymentDetails = void 0;
const core = __importStar(require("../../core"));
const Card_1 = require("./Card");
const DeviceDetails_1 = require("./DeviceDetails");
const CardPaymentTimeline_1 = require("./CardPaymentTimeline");
const Error_1 = require("./Error_");
exports.CardPaymentDetails = core.serialization.object({
    status: core.serialization.string().optionalNullable(),
    card: Card_1.Card.optional(),
    entryMethod: core.serialization.property("entry_method", core.serialization.string().optionalNullable()),
    cvvStatus: core.serialization.property("cvv_status", core.serialization.string().optionalNullable()),
    avsStatus: core.serialization.property("avs_status", core.serialization.string().optionalNullable()),
    authResultCode: core.serialization.property("auth_result_code", core.serialization.string().optionalNullable()),
    applicationIdentifier: core.serialization.property("application_identifier", core.serialization.string().optionalNullable()),
    applicationName: core.serialization.property("application_name", core.serialization.string().optionalNullable()),
    applicationCryptogram: core.serialization.property("application_cryptogram", core.serialization.string().optionalNullable()),
    verificationMethod: core.serialization.property("verification_method", core.serialization.string().optionalNullable()),
    verificationResults: core.serialization.property("verification_results", core.serialization.string().optionalNullable()),
    statementDescription: core.serialization.property("statement_description", core.serialization.string().optionalNullable()),
    deviceDetails: core.serialization.property("device_details", DeviceDetails_1.DeviceDetails.optional()),
    cardPaymentTimeline: core.serialization.property("card_payment_timeline", CardPaymentTimeline_1.CardPaymentTimeline.optional()),
    refundRequiresCardPresence: core.serialization.property("refund_requires_card_presence", core.serialization.boolean().optionalNullable()),
    errors: core.serialization.list(Error_1.Error_).optionalNullable(),
});
