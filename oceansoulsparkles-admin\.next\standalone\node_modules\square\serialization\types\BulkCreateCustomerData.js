"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkCreateCustomerData = void 0;
const core = __importStar(require("../../core"));
const Address_1 = require("./Address");
const CustomerTaxIds_1 = require("./CustomerTaxIds");
exports.BulkCreateCustomerData = core.serialization.object({
    givenName: core.serialization.property("given_name", core.serialization.string().optionalNullable()),
    familyName: core.serialization.property("family_name", core.serialization.string().optionalNullable()),
    companyName: core.serialization.property("company_name", core.serialization.string().optionalNullable()),
    nickname: core.serialization.string().optionalNullable(),
    emailAddress: core.serialization.property("email_address", core.serialization.string().optionalNullable()),
    address: Address_1.Address.optional(),
    phoneNumber: core.serialization.property("phone_number", core.serialization.string().optionalNullable()),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optionalNullable()),
    note: core.serialization.string().optionalNullable(),
    birthday: core.serialization.string().optionalNullable(),
    taxIds: core.serialization.property("tax_ids", CustomerTaxIds_1.CustomerTaxIds.optional()),
});
