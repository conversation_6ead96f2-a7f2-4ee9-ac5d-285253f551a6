"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FulfillmentPickupDetails = void 0;
const core = __importStar(require("../../core"));
const FulfillmentRecipient_1 = require("./FulfillmentRecipient");
const FulfillmentPickupDetailsScheduleType_1 = require("./FulfillmentPickupDetailsScheduleType");
const FulfillmentPickupDetailsCurbsidePickupDetails_1 = require("./FulfillmentPickupDetailsCurbsidePickupDetails");
exports.FulfillmentPickupDetails = core.serialization.object({
    recipient: FulfillmentRecipient_1.FulfillmentRecipient.optional(),
    expiresAt: core.serialization.property("expires_at", core.serialization.string().optionalNullable()),
    autoCompleteDuration: core.serialization.property("auto_complete_duration", core.serialization.string().optionalNullable()),
    scheduleType: core.serialization.property("schedule_type", FulfillmentPickupDetailsScheduleType_1.FulfillmentPickupDetailsScheduleType.optional()),
    pickupAt: core.serialization.property("pickup_at", core.serialization.string().optionalNullable()),
    pickupWindowDuration: core.serialization.property("pickup_window_duration", core.serialization.string().optionalNullable()),
    prepTimeDuration: core.serialization.property("prep_time_duration", core.serialization.string().optionalNullable()),
    note: core.serialization.string().optionalNullable(),
    placedAt: core.serialization.property("placed_at", core.serialization.string().optional()),
    acceptedAt: core.serialization.property("accepted_at", core.serialization.string().optional()),
    rejectedAt: core.serialization.property("rejected_at", core.serialization.string().optional()),
    readyAt: core.serialization.property("ready_at", core.serialization.string().optional()),
    expiredAt: core.serialization.property("expired_at", core.serialization.string().optional()),
    pickedUpAt: core.serialization.property("picked_up_at", core.serialization.string().optional()),
    canceledAt: core.serialization.property("canceled_at", core.serialization.string().optional()),
    cancelReason: core.serialization.property("cancel_reason", core.serialization.string().optionalNullable()),
    isCurbsidePickup: core.serialization.property("is_curbside_pickup", core.serialization.boolean().optionalNullable()),
    curbsidePickupDetails: core.serialization.property("curbside_pickup_details", FulfillmentPickupDetailsCurbsidePickupDetails_1.FulfillmentPickupDetailsCurbsidePickupDetails.optional()),
});
