"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryAdjustment = void 0;
const core = __importStar(require("../../core"));
const InventoryState_1 = require("./InventoryState");
const Money_1 = require("./Money");
const SourceApplication_1 = require("./SourceApplication");
const InventoryAdjustmentGroup_1 = require("./InventoryAdjustmentGroup");
exports.InventoryAdjustment = core.serialization.object({
    id: core.serialization.string().optional(),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optionalNullable()),
    fromState: core.serialization.property("from_state", InventoryState_1.InventoryState.optional()),
    toState: core.serialization.property("to_state", InventoryState_1.InventoryState.optional()),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    catalogObjectId: core.serialization.property("catalog_object_id", core.serialization.string().optionalNullable()),
    catalogObjectType: core.serialization.property("catalog_object_type", core.serialization.string().optionalNullable()),
    quantity: core.serialization.string().optionalNullable(),
    totalPriceMoney: core.serialization.property("total_price_money", Money_1.Money.optional()),
    occurredAt: core.serialization.property("occurred_at", core.serialization.string().optionalNullable()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    source: SourceApplication_1.SourceApplication.optional(),
    employeeId: core.serialization.property("employee_id", core.serialization.string().optionalNullable()),
    teamMemberId: core.serialization.property("team_member_id", core.serialization.string().optionalNullable()),
    transactionId: core.serialization.property("transaction_id", core.serialization.string().optional()),
    refundId: core.serialization.property("refund_id", core.serialization.string().optional()),
    purchaseOrderId: core.serialization.property("purchase_order_id", core.serialization.string().optional()),
    goodsReceiptId: core.serialization.property("goods_receipt_id", core.serialization.string().optional()),
    adjustmentGroup: core.serialization.property("adjustment_group", InventoryAdjustmentGroup_1.InventoryAdjustmentGroup.optional()),
});
