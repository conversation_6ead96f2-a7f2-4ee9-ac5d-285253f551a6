"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Card = void 0;
const core = __importStar(require("../../core"));
const CardBrand_1 = require("./CardBrand");
const Address_1 = require("./Address");
const CardType_1 = require("./CardType");
const CardPrepaidType_1 = require("./CardPrepaidType");
const CardCoBrand_1 = require("./CardCoBrand");
const CardIssuerAlert_1 = require("./CardIssuerAlert");
exports.Card = core.serialization.object({
    id: core.serialization.string().optional(),
    cardBrand: core.serialization.property("card_brand", CardBrand_1.CardBrand.optional()),
    last4: core.serialization.property("last_4", core.serialization.string().optional()),
    expMonth: core.serialization.property("exp_month", core.serialization.bigint().optionalNullable()),
    expYear: core.serialization.property("exp_year", core.serialization.bigint().optionalNullable()),
    cardholderName: core.serialization.property("cardholder_name", core.serialization.string().optionalNullable()),
    billingAddress: core.serialization.property("billing_address", Address_1.Address.optional()),
    fingerprint: core.serialization.string().optional(),
    customerId: core.serialization.property("customer_id", core.serialization.string().optionalNullable()),
    merchantId: core.serialization.property("merchant_id", core.serialization.string().optional()),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optionalNullable()),
    enabled: core.serialization.boolean().optional(),
    cardType: core.serialization.property("card_type", CardType_1.CardType.optional()),
    prepaidType: core.serialization.property("prepaid_type", CardPrepaidType_1.CardPrepaidType.optional()),
    bin: core.serialization.string().optional(),
    version: core.serialization.bigint().optional(),
    cardCoBrand: core.serialization.property("card_co_brand", CardCoBrand_1.CardCoBrand.optional()),
    issuerAlert: core.serialization.property("issuer_alert", CardIssuerAlert_1.CardIssuerAlert.optional()),
    issuerAlertAt: core.serialization.property("issuer_alert_at", core.serialization.string().optional()),
    hsaFsa: core.serialization.property("hsa_fsa", core.serialization.boolean().optional()),
});
