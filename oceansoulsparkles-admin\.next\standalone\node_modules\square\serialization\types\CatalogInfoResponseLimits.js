"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogInfoResponseLimits = void 0;
const core = __importStar(require("../../core"));
exports.CatalogInfoResponseLimits = core.serialization.object({
    batchUpsertMaxObjectsPerBatch: core.serialization.property("batch_upsert_max_objects_per_batch", core.serialization.number().optionalNullable()),
    batchUpsertMaxTotalObjects: core.serialization.property("batch_upsert_max_total_objects", core.serialization.number().optionalNullable()),
    batchRetrieveMaxObjectIds: core.serialization.property("batch_retrieve_max_object_ids", core.serialization.number().optionalNullable()),
    searchMaxPageLimit: core.serialization.property("search_max_page_limit", core.serialization.number().optionalNullable()),
    batchDeleteMaxObjectIds: core.serialization.property("batch_delete_max_object_ids", core.serialization.number().optionalNullable()),
    updateItemTaxesMaxItemIds: core.serialization.property("update_item_taxes_max_item_ids", core.serialization.number().optionalNullable()),
    updateItemTaxesMaxTaxesToEnable: core.serialization.property("update_item_taxes_max_taxes_to_enable", core.serialization.number().optionalNullable()),
    updateItemTaxesMaxTaxesToDisable: core.serialization.property("update_item_taxes_max_taxes_to_disable", core.serialization.number().optionalNullable()),
    updateItemModifierListsMaxItemIds: core.serialization.property("update_item_modifier_lists_max_item_ids", core.serialization.number().optionalNullable()),
    updateItemModifierListsMaxModifierListsToEnable: core.serialization.property("update_item_modifier_lists_max_modifier_lists_to_enable", core.serialization.number().optionalNullable()),
    updateItemModifierListsMaxModifierListsToDisable: core.serialization.property("update_item_modifier_lists_max_modifier_lists_to_disable", core.serialization.number().optionalNullable()),
});
