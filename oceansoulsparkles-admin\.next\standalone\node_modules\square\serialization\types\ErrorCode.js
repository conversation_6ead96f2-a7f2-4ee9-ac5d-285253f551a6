"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCode = void 0;
const core = __importStar(require("../../core"));
exports.ErrorCode = core.serialization.enum_([
    "INTERNAL_SERVER_ERROR",
    "UNAUTHORIZED",
    "ACCESS_TOKEN_EXPIRED",
    "ACCESS_TOKEN_REVOKED",
    "CLIENT_DISABLED",
    "FORBIDDEN",
    "INSUFFICIENT_SCOPES",
    "APPLICATION_DISABLED",
    "V1_APPLICATION",
    "V1_ACCESS_TOKEN",
    "CARD_PROCESSING_NOT_ENABLED",
    "MERCHANT_SUBSCRIPTION_NOT_FOUND",
    "BAD_REQUEST",
    "MISSING_REQUIRED_PARAMETER",
    "INCORRECT_TYPE",
    "INVALID_TIME",
    "INVALID_TIME_RANGE",
    "INVALID_VALUE",
    "INVALID_CURSOR",
    "UNKNOWN_QUERY_PARAMETER",
    "CONFLICTING_PARAMETERS",
    "EXPECTED_JSON_BODY",
    "INVALID_SORT_ORDER",
    "VALUE_REGEX_MISMATCH",
    "VALUE_TOO_SHORT",
    "VALUE_TOO_LONG",
    "VALUE_TOO_LOW",
    "VALUE_TOO_HIGH",
    "VALUE_EMPTY",
    "ARRAY_LENGTH_TOO_LONG",
    "ARRAY_LENGTH_TOO_SHORT",
    "ARRAY_EMPTY",
    "EXPECTED_BOOLEAN",
    "EXPECTED_INTEGER",
    "EXPECTED_FLOAT",
    "EXPECTED_STRING",
    "EXPECTED_OBJECT",
    "EXPECTED_ARRAY",
    "EXPECTED_MAP",
    "EXPECTED_BASE64_ENCODED_BYTE_ARRAY",
    "INVALID_ARRAY_VALUE",
    "INVALID_ENUM_VALUE",
    "INVALID_CONTENT_TYPE",
    "INVALID_FORM_VALUE",
    "CUSTOMER_NOT_FOUND",
    "ONE_INSTRUMENT_EXPECTED",
    "NO_FIELDS_SET",
    "TOO_MANY_MAP_ENTRIES",
    "MAP_KEY_LENGTH_TOO_SHORT",
    "MAP_KEY_LENGTH_TOO_LONG",
    "CUSTOMER_MISSING_NAME",
    "CUSTOMER_MISSING_EMAIL",
    "INVALID_PAUSE_LENGTH",
    "INVALID_DATE",
    "UNSUPPORTED_COUNTRY",
    "UNSUPPORTED_CURRENCY",
    "APPLE_TTP_PIN_TOKEN",
    "CARD_EXPIRED",
    "INVALID_EXPIRATION",
    "INVALID_EXPIRATION_YEAR",
    "INVALID_EXPIRATION_DATE",
    "UNSUPPORTED_CARD_BRAND",
    "UNSUPPORTED_ENTRY_METHOD",
    "INVALID_ENCRYPTED_CARD",
    "INVALID_CARD",
    "PAYMENT_AMOUNT_MISMATCH",
    "GENERIC_DECLINE",
    "CVV_FAILURE",
    "ADDRESS_VERIFICATION_FAILURE",
    "INVALID_ACCOUNT",
    "CURRENCY_MISMATCH",
    "INSUFFICIENT_FUNDS",
    "INSUFFICIENT_PERMISSIONS",
    "CARDHOLDER_INSUFFICIENT_PERMISSIONS",
    "INVALID_LOCATION",
    "TRANSACTION_LIMIT",
    "VOICE_FAILURE",
    "PAN_FAILURE",
    "EXPIRATION_FAILURE",
    "CARD_NOT_SUPPORTED",
    "READER_DECLINED",
    "INVALID_PIN",
    "MISSING_PIN",
    "MISSING_ACCOUNT_TYPE",
    "INVALID_POSTAL_CODE",
    "INVALID_FEES",
    "MANUALLY_ENTERED_PAYMENT_NOT_SUPPORTED",
    "PAYMENT_LIMIT_EXCEEDED",
    "GIFT_CARD_AVAILABLE_AMOUNT",
    "ACCOUNT_UNUSABLE",
    "BUYER_REFUSED_PAYMENT",
    "DELAYED_TRANSACTION_EXPIRED",
    "DELAYED_TRANSACTION_CANCELED",
    "DELAYED_TRANSACTION_CAPTURED",
    "DELAYED_TRANSACTION_FAILED",
    "CARD_TOKEN_EXPIRED",
    "CARD_TOKEN_USED",
    "AMOUNT_TOO_HIGH",
    "UNSUPPORTED_INSTRUMENT_TYPE",
    "REFUND_AMOUNT_INVALID",
    "REFUND_ALREADY_PENDING",
    "PAYMENT_NOT_REFUNDABLE",
    "PAYMENT_NOT_REFUNDABLE_DUE_TO_DISPUTE",
    "REFUND_ERROR_PAYMENT_NEEDS_COMPLETION",
    "REFUND_DECLINED",
    "INSUFFICIENT_PERMISSIONS_FOR_REFUND",
    "INVALID_CARD_DATA",
    "SOURCE_USED",
    "SOURCE_EXPIRED",
    "UNSUPPORTED_LOYALTY_REWARD_TIER",
    "LOCATION_MISMATCH",
    "ORDER_UNPAID_NOT_RETURNABLE",
    "IDEMPOTENCY_KEY_REUSED",
    "UNEXPECTED_VALUE",
    "SANDBOX_NOT_SUPPORTED",
    "INVALID_EMAIL_ADDRESS",
    "INVALID_PHONE_NUMBER",
    "CHECKOUT_EXPIRED",
    "BAD_CERTIFICATE",
    "INVALID_SQUARE_VERSION_FORMAT",
    "API_VERSION_INCOMPATIBLE",
    "CARD_PRESENCE_REQUIRED",
    "UNSUPPORTED_SOURCE_TYPE",
    "CARD_MISMATCH",
    "PLAID_ERROR",
    "PLAID_ERROR_ITEM_LOGIN_REQUIRED",
    "PLAID_ERROR_RATE_LIMIT",
    "CARD_DECLINED",
    "VERIFY_CVV_FAILURE",
    "VERIFY_AVS_FAILURE",
    "CARD_DECLINED_CALL_ISSUER",
    "CARD_DECLINED_VERIFICATION_REQUIRED",
    "BAD_EXPIRATION",
    "CHIP_INSERTION_REQUIRED",
    "ALLOWABLE_PIN_TRIES_EXCEEDED",
    "RESERVATION_DECLINED",
    "UNKNOWN_BODY_PARAMETER",
    "NOT_FOUND",
    "APPLE_PAYMENT_PROCESSING_CERTIFICATE_HASH_NOT_FOUND",
    "METHOD_NOT_ALLOWED",
    "NOT_ACCEPTABLE",
    "REQUEST_TIMEOUT",
    "CONFLICT",
    "GONE",
    "REQUEST_ENTITY_TOO_LARGE",
    "UNSUPPORTED_MEDIA_TYPE",
    "UNPROCESSABLE_ENTITY",
    "RATE_LIMITED",
    "NOT_IMPLEMENTED",
    "BAD_GATEWAY",
    "SERVICE_UNAVAILABLE",
    "TEMPORARY_ERROR",
    "GATEWAY_TIMEOUT",
]);
