"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dispute = void 0;
const core = __importStar(require("../../core"));
const Money_1 = require("./Money");
const DisputeReason_1 = require("./DisputeReason");
const DisputeState_1 = require("./DisputeState");
const DisputedPayment_1 = require("./DisputedPayment");
const CardBrand_1 = require("./CardBrand");
exports.Dispute = core.serialization.object({
    disputeId: core.serialization.property("dispute_id", core.serialization.string().optionalNullable()),
    id: core.serialization.string().optional(),
    amountMoney: core.serialization.property("amount_money", Money_1.Money.optional()),
    reason: DisputeReason_1.DisputeReason.optional(),
    state: DisputeState_1.DisputeState.optional(),
    dueAt: core.serialization.property("due_at", core.serialization.string().optionalNullable()),
    disputedPayment: core.serialization.property("disputed_payment", DisputedPayment_1.DisputedPayment.optional()),
    evidenceIds: core.serialization.property("evidence_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    cardBrand: core.serialization.property("card_brand", CardBrand_1.CardBrand.optional()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    updatedAt: core.serialization.property("updated_at", core.serialization.string().optional()),
    brandDisputeId: core.serialization.property("brand_dispute_id", core.serialization.string().optionalNullable()),
    reportedDate: core.serialization.property("reported_date", core.serialization.string().optionalNullable()),
    reportedAt: core.serialization.property("reported_at", core.serialization.string().optionalNullable()),
    version: core.serialization.number().optional(),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
});
