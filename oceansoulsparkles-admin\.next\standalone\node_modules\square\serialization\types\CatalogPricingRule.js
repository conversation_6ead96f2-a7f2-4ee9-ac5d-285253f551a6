"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogPricingRule = void 0;
const core = __importStar(require("../../core"));
const ExcludeStrategy_1 = require("./ExcludeStrategy");
const Money_1 = require("./Money");
exports.CatalogPricingRule = core.serialization.object({
    name: core.serialization.string().optionalNullable(),
    timePeriodIds: core.serialization.property("time_period_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    discountId: core.serialization.property("discount_id", core.serialization.string().optionalNullable()),
    matchProductsId: core.serialization.property("match_products_id", core.serialization.string().optionalNullable()),
    applyProductsId: core.serialization.property("apply_products_id", core.serialization.string().optionalNullable()),
    excludeProductsId: core.serialization.property("exclude_products_id", core.serialization.string().optionalNullable()),
    validFromDate: core.serialization.property("valid_from_date", core.serialization.string().optionalNullable()),
    validFromLocalTime: core.serialization.property("valid_from_local_time", core.serialization.string().optionalNullable()),
    validUntilDate: core.serialization.property("valid_until_date", core.serialization.string().optionalNullable()),
    validUntilLocalTime: core.serialization.property("valid_until_local_time", core.serialization.string().optionalNullable()),
    excludeStrategy: core.serialization.property("exclude_strategy", ExcludeStrategy_1.ExcludeStrategy.optional()),
    minimumOrderSubtotalMoney: core.serialization.property("minimum_order_subtotal_money", Money_1.Money.optional()),
    customerGroupIdsAny: core.serialization.property("customer_group_ids_any", core.serialization.list(core.serialization.string()).optionalNullable()),
});
