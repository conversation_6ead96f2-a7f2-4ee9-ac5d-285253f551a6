"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerFilter = void 0;
const core = __importStar(require("../../core"));
const CustomerCreationSourceFilter_1 = require("./CustomerCreationSourceFilter");
const TimeRange_1 = require("./TimeRange");
const CustomerTextFilter_1 = require("./CustomerTextFilter");
const FilterValue_1 = require("./FilterValue");
const CustomerCustomAttributeFilters_1 = require("./CustomerCustomAttributeFilters");
exports.CustomerFilter = core.serialization.object({
    creationSource: core.serialization.property("creation_source", CustomerCreationSourceFilter_1.CustomerCreationSourceFilter.optional()),
    createdAt: core.serialization.property("created_at", TimeRange_1.TimeRange.optional()),
    updatedAt: core.serialization.property("updated_at", TimeRange_1.TimeRange.optional()),
    emailAddress: core.serialization.property("email_address", CustomerTextFilter_1.CustomerTextFilter.optional()),
    phoneNumber: core.serialization.property("phone_number", CustomerTextFilter_1.CustomerTextFilter.optional()),
    referenceId: core.serialization.property("reference_id", CustomerTextFilter_1.CustomerTextFilter.optional()),
    groupIds: core.serialization.property("group_ids", FilterValue_1.FilterValue.optional()),
    customAttribute: core.serialization.property("custom_attribute", CustomerCustomAttributeFilters_1.CustomerCustomAttributeFilters.optional()),
    segmentIds: core.serialization.property("segment_ids", FilterValue_1.FilterValue.optional()),
});
