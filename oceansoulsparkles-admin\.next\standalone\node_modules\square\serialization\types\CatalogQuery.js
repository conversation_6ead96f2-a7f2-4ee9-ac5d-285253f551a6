"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogQuery = void 0;
const core = __importStar(require("../../core"));
const CatalogQuerySortedAttribute_1 = require("./CatalogQuerySortedAttribute");
const CatalogQueryExact_1 = require("./CatalogQueryExact");
const CatalogQuerySet_1 = require("./CatalogQuerySet");
const CatalogQueryPrefix_1 = require("./CatalogQueryPrefix");
const CatalogQueryRange_1 = require("./CatalogQueryRange");
const CatalogQueryText_1 = require("./CatalogQueryText");
const CatalogQueryItemsForTax_1 = require("./CatalogQueryItemsForTax");
const CatalogQueryItemsForModifierList_1 = require("./CatalogQueryItemsForModifierList");
const CatalogQueryItemsForItemOptions_1 = require("./CatalogQueryItemsForItemOptions");
const CatalogQueryItemVariationsForItemOptionValues_1 = require("./CatalogQueryItemVariationsForItemOptionValues");
exports.CatalogQuery = core.serialization.object({
    sortedAttributeQuery: core.serialization.property("sorted_attribute_query", CatalogQuerySortedAttribute_1.CatalogQuerySortedAttribute.optional()),
    exactQuery: core.serialization.property("exact_query", CatalogQueryExact_1.CatalogQueryExact.optional()),
    setQuery: core.serialization.property("set_query", CatalogQuerySet_1.CatalogQuerySet.optional()),
    prefixQuery: core.serialization.property("prefix_query", CatalogQueryPrefix_1.CatalogQueryPrefix.optional()),
    rangeQuery: core.serialization.property("range_query", CatalogQueryRange_1.CatalogQueryRange.optional()),
    textQuery: core.serialization.property("text_query", CatalogQueryText_1.CatalogQueryText.optional()),
    itemsForTaxQuery: core.serialization.property("items_for_tax_query", CatalogQueryItemsForTax_1.CatalogQueryItemsForTax.optional()),
    itemsForModifierListQuery: core.serialization.property("items_for_modifier_list_query", CatalogQueryItemsForModifierList_1.CatalogQueryItemsForModifierList.optional()),
    itemsForItemOptionsQuery: core.serialization.property("items_for_item_options_query", CatalogQueryItemsForItemOptions_1.CatalogQueryItemsForItemOptions.optional()),
    itemVariationsForItemOptionValuesQuery: core.serialization.property("item_variations_for_item_option_values_query", CatalogQueryItemVariationsForItemOptionValues_1.CatalogQueryItemVariationsForItemOptionValues.optional()),
});
