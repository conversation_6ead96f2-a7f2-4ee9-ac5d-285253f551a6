"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerCustomAttributeFilterValue = void 0;
const core = __importStar(require("../../core"));
const CustomerTextFilter_1 = require("./CustomerTextFilter");
const FilterValue_1 = require("./FilterValue");
const TimeRange_1 = require("./TimeRange");
const FloatNumberRange_1 = require("./FloatNumberRange");
const CustomerAddressFilter_1 = require("./CustomerAddressFilter");
exports.CustomerCustomAttributeFilterValue = core.serialization.object({
    email: CustomerTextFilter_1.CustomerTextFilter.optional(),
    phone: CustomerTextFilter_1.CustomerTextFilter.optional(),
    text: CustomerTextFilter_1.CustomerTextFilter.optional(),
    selection: FilterValue_1.FilterValue.optional(),
    date: TimeRange_1.TimeRange.optional(),
    number: FloatNumberRange_1.FloatNumberRange.optional(),
    boolean: core.serialization.boolean().optionalNullable(),
    address: CustomerAddressFilter_1.CustomerAddressFilter.optional(),
});
