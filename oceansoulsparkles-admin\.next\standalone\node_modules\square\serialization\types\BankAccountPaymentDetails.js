"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankAccountPaymentDetails = void 0;
const core = __importStar(require("../../core"));
const AchDetails_1 = require("./AchDetails");
const Error_1 = require("./Error_");
exports.BankAccountPaymentDetails = core.serialization.object({
    bankName: core.serialization.property("bank_name", core.serialization.string().optionalNullable()),
    transferType: core.serialization.property("transfer_type", core.serialization.string().optionalNullable()),
    accountOwnershipType: core.serialization.property("account_ownership_type", core.serialization.string().optionalNullable()),
    fingerprint: core.serialization.string().optionalNullable(),
    country: core.serialization.string().optionalNullable(),
    statementDescription: core.serialization.property("statement_description", core.serialization.string().optionalNullable()),
    achDetails: core.serialization.property("ach_details", AchDetails_1.AchDetails.optional()),
    errors: core.serialization.list(Error_1.Error_).optionalNullable(),
});
