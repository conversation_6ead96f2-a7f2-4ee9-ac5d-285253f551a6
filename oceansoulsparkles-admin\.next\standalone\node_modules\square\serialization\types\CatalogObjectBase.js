"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogObjectBase = void 0;
const core = __importStar(require("../../core"));
const CatalogCustomAttributeValue_1 = require("./CatalogCustomAttributeValue");
const CatalogV1Id_1 = require("./CatalogV1Id");
exports.CatalogObjectBase = core.serialization.object({
    id: core.serialization.string(),
    updatedAt: core.serialization.property("updated_at", core.serialization.string().optional()),
    version: core.serialization.bigint().optional(),
    isDeleted: core.serialization.property("is_deleted", core.serialization.boolean().optional()),
    customAttributeValues: core.serialization.property("custom_attribute_values", core.serialization.record(core.serialization.string(), CatalogCustomAttributeValue_1.CatalogCustomAttributeValue).optional()),
    catalogV1Ids: core.serialization.property("catalog_v1_ids", core.serialization.list(CatalogV1Id_1.CatalogV1Id).optional()),
    presentAtAllLocations: core.serialization.property("present_at_all_locations", core.serialization.boolean().optional()),
    presentAtLocationIds: core.serialization.property("present_at_location_ids", core.serialization.list(core.serialization.string()).optional()),
    absentAtLocationIds: core.serialization.property("absent_at_location_ids", core.serialization.list(core.serialization.string()).optional()),
    imageId: core.serialization.property("image_id", core.serialization.string().optional()),
});
