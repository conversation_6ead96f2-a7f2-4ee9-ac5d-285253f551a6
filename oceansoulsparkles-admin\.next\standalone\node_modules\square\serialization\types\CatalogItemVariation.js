"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogItemVariation = void 0;
const core = __importStar(require("../../core"));
const CatalogPricingType_1 = require("./CatalogPricingType");
const Money_1 = require("./Money");
const ItemVariationLocationOverrides_1 = require("./ItemVariationLocationOverrides");
const InventoryAlertType_1 = require("./InventoryAlertType");
const CatalogItemOptionValueForItemVariation_1 = require("./CatalogItemOptionValueForItemVariation");
const CatalogStockConversion_1 = require("./CatalogStockConversion");
exports.CatalogItemVariation = core.serialization.object({
    itemId: core.serialization.property("item_id", core.serialization.string().optionalNullable()),
    name: core.serialization.string().optionalNullable(),
    sku: core.serialization.string().optionalNullable(),
    upc: core.serialization.string().optionalNullable(),
    ordinal: core.serialization.number().optional(),
    pricingType: core.serialization.property("pricing_type", CatalogPricingType_1.CatalogPricingType.optional()),
    priceMoney: core.serialization.property("price_money", Money_1.Money.optional()),
    locationOverrides: core.serialization.property("location_overrides", core.serialization.list(ItemVariationLocationOverrides_1.ItemVariationLocationOverrides).optionalNullable()),
    trackInventory: core.serialization.property("track_inventory", core.serialization.boolean().optionalNullable()),
    inventoryAlertType: core.serialization.property("inventory_alert_type", InventoryAlertType_1.InventoryAlertType.optional()),
    inventoryAlertThreshold: core.serialization.property("inventory_alert_threshold", core.serialization.bigint().optionalNullable()),
    userData: core.serialization.property("user_data", core.serialization.string().optionalNullable()),
    serviceDuration: core.serialization.property("service_duration", core.serialization.bigint().optionalNullable()),
    availableForBooking: core.serialization.property("available_for_booking", core.serialization.boolean().optionalNullable()),
    itemOptionValues: core.serialization.property("item_option_values", core.serialization.list(CatalogItemOptionValueForItemVariation_1.CatalogItemOptionValueForItemVariation).optionalNullable()),
    measurementUnitId: core.serialization.property("measurement_unit_id", core.serialization.string().optionalNullable()),
    sellable: core.serialization.boolean().optionalNullable(),
    stockable: core.serialization.boolean().optionalNullable(),
    imageIds: core.serialization.property("image_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    teamMemberIds: core.serialization.property("team_member_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    stockableConversion: core.serialization.property("stockable_conversion", CatalogStockConversion_1.CatalogStockConversion.optional()),
});
