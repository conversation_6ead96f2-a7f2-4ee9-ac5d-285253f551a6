"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashDrawerShiftSummary = void 0;
const core = __importStar(require("../../core"));
const CashDrawerShiftState_1 = require("./CashDrawerShiftState");
const Money_1 = require("./Money");
exports.CashDrawerShiftSummary = core.serialization.object({
    id: core.serialization.string().optional(),
    state: CashDrawerShiftState_1.CashDrawerShiftState.optional(),
    openedAt: core.serialization.property("opened_at", core.serialization.string().optionalNullable()),
    endedAt: core.serialization.property("ended_at", core.serialization.string().optionalNullable()),
    closedAt: core.serialization.property("closed_at", core.serialization.string().optionalNullable()),
    description: core.serialization.string().optionalNullable(),
    openedCashMoney: core.serialization.property("opened_cash_money", Money_1.Money.optional()),
    expectedCashMoney: core.serialization.property("expected_cash_money", Money_1.Money.optional()),
    closedCashMoney: core.serialization.property("closed_cash_money", Money_1.Money.optional()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    updatedAt: core.serialization.property("updated_at", core.serialization.string().optional()),
    locationId: core.serialization.property("location_id", core.serialization.string().optional()),
});
