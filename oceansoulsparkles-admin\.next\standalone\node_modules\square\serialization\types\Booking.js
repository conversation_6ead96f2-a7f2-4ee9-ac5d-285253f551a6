"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Booking = void 0;
const core = __importStar(require("../../core"));
const BookingStatus_1 = require("./BookingStatus");
const AppointmentSegment_1 = require("./AppointmentSegment");
const BusinessAppointmentSettingsBookingLocationType_1 = require("./BusinessAppointmentSettingsBookingLocationType");
const BookingCreatorDetails_1 = require("./BookingCreatorDetails");
const BookingBookingSource_1 = require("./BookingBookingSource");
const Address_1 = require("./Address");
exports.Booking = core.serialization.object({
    id: core.serialization.string().optional(),
    version: core.serialization.number().optional(),
    status: BookingStatus_1.BookingStatus.optional(),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    updatedAt: core.serialization.property("updated_at", core.serialization.string().optional()),
    startAt: core.serialization.property("start_at", core.serialization.string().optionalNullable()),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    customerId: core.serialization.property("customer_id", core.serialization.string().optionalNullable()),
    customerNote: core.serialization.property("customer_note", core.serialization.string().optionalNullable()),
    sellerNote: core.serialization.property("seller_note", core.serialization.string().optionalNullable()),
    appointmentSegments: core.serialization.property("appointment_segments", core.serialization.list(AppointmentSegment_1.AppointmentSegment).optionalNullable()),
    transitionTimeMinutes: core.serialization.property("transition_time_minutes", core.serialization.number().optional()),
    allDay: core.serialization.property("all_day", core.serialization.boolean().optional()),
    locationType: core.serialization.property("location_type", BusinessAppointmentSettingsBookingLocationType_1.BusinessAppointmentSettingsBookingLocationType.optional()),
    creatorDetails: core.serialization.property("creator_details", BookingCreatorDetails_1.BookingCreatorDetails.optional()),
    source: BookingBookingSource_1.BookingBookingSource.optional(),
    address: Address_1.Address.optional(),
});
