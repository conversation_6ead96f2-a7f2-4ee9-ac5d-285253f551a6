(()=>{var e={};e.id=9189,e.ids=[9189,660],e.modules={4748:e=>{e.exports={activityFeedContainer:"ActivityFeed_activityFeedContainer__4YLS2",header:"ActivityFeed_header__ebYB5",sectionTitle:"ActivityFeed_sectionTitle__7wsra",sectionSubtitle:"ActivityFeed_sectionSubtitle__wvFSj",activityList:"ActivityFeed_activityList__wFX9_",emptyState:"ActivityFeed_emptyState__c8vle",emptyIcon:"ActivityFeed_emptyIcon__iyb1U",activityItem:"ActivityFeed_activityItem__ojhE_",activityIconContainer:"ActivityFeed_activityIconContainer__VxaNC",activityIcon:"ActivityFeed_activityIcon__hKpNq",activityLine:"ActivityFeed_activityLine__HXZH6",activityContent:"ActivityFeed_activityContent__8z0fW",activityHeader:"ActivityFeed_activityHeader__8njfW",activityTitle:"ActivityFeed_activityTitle__oYur8",activityTime:"ActivityFeed_activityTime__ZxRaZ",activityDescription:"ActivityFeed_activityDescription__lLdMz",activityUser:"ActivityFeed_activityUser__YpFAI",userIcon:"ActivityFeed_userIcon__1oTVE",userName:"ActivityFeed_userName__uV1HV",activityMetadata:"ActivityFeed_activityMetadata__n0_sP",metadataItem:"ActivityFeed_metadataItem__XwPmn",activitySummary:"ActivityFeed_activitySummary__Oz32n",summaryItem:"ActivityFeed_summaryItem__DOtF2",summaryIcon:"ActivityFeed_summaryIcon__EZt5H",summaryText:"ActivityFeed_summaryText__cegn7",summaryValue:"ActivityFeed_summaryValue__KuksP",summaryLabel:"ActivityFeed_summaryLabel__2WPdu"}},9422:e=>{e.exports={dashboard:"Dashboard_dashboard__C_xzw",header:"Dashboard_header__jEMlB",title:"Dashboard_title__J6tRQ",userInfo:"Dashboard_userInfo__CSKi4",userEmail:"Dashboard_userEmail__YOWKz",logoutButton:"Dashboard_logoutButton__v5jTs",tabs:"Dashboard_tabs__mlim2",tabButton:"Dashboard_tabButton__MTrqh",active:"Dashboard_active__Inrb7",tabContent:"Dashboard_tabContent__mjSdZ",tabHeader:"Dashboard_tabHeader__mNHk1",tabTitle:"Dashboard_tabTitle__7nAof",tabActions:"Dashboard_tabActions__Z2yF4",newButton:"Dashboard_newButton__TzZpK",refreshButton:"Dashboard_refreshButton__jDFO0",statsGrid:"Dashboard_statsGrid__9_bUr",statCard:"Dashboard_statCard__w6R_r",statValue:"Dashboard_statValue__hRgVA",statChange:"Dashboard_statChange__KnWEf",quickActions:"Dashboard_quickActions__lw9nA",actionGrid:"Dashboard_actionGrid__v0ovq",actionCard:"Dashboard_actionCard__x4xCI",comingSoon:"Dashboard_comingSoon__dps_k",loadingContainer:"Dashboard_loadingContainer__SbWL6",authContainer:"Dashboard_authContainer__gM33g",loadingSpinner:"Dashboard_loadingSpinner__AGCuq",spin:"Dashboard_spin___wsH9",loginButton:"Dashboard_loginButton__RRWbK"}},2998:e=>{e.exports={statsContainer:"DashboardStats_statsContainer__ce4NH",sectionTitle:"DashboardStats_sectionTitle__EsUSy",statsGrid:"DashboardStats_statsGrid__uqCUy",statCard:"DashboardStats_statCard__zSy1D",statHeader:"DashboardStats_statHeader__TMAf5",statIcon:"DashboardStats_statIcon__u_dzE",statTrend:"DashboardStats_statTrend__eb2Yt",statBadge:"DashboardStats_statBadge__ksgAD",statIndicator:"DashboardStats_statIndicator__NlR1D",statusDot:"DashboardStats_statusDot__Cjyhb",pulse:"DashboardStats_pulse__oghZ9",statValue:"DashboardStats_statValue__5J9wh",statLabel:"DashboardStats_statLabel__3P73_",statSubtext:"DashboardStats_statSubtext__V6hFX",breakdownSection:"DashboardStats_breakdownSection__zRN0m",breakdownTitle:"DashboardStats_breakdownTitle__eUsQQ",breakdownGrid:"DashboardStats_breakdownGrid__Zvkyd",breakdownCard:"DashboardStats_breakdownCard__hAD_q",breakdownIcon:"DashboardStats_breakdownIcon__taBmW",breakdownContent:"DashboardStats_breakdownContent__44Tcq",breakdownValue:"DashboardStats_breakdownValue__UPKQ8",breakdownLabel:"DashboardStats_breakdownLabel__ia_lf"}},9889:e=>{e.exports={quickActionsContainer:"QuickActions_quickActionsContainer__stJ_Q",header:"QuickActions_header__cOTXt",sectionTitle:"QuickActions_sectionTitle__q0GoQ",sectionSubtitle:"QuickActions_sectionSubtitle__58sxX",actionsGrid:"QuickActions_actionsGrid__7EXnk",actionCard:"QuickActions_actionCard___OnUN",actionIcon:"QuickActions_actionIcon__uMNZ4",actionContent:"QuickActions_actionContent__Zv2vY",actionTitle:"QuickActions_actionTitle__UFiU1",actionDescription:"QuickActions_actionDescription__DjvIe",actionArrow:"QuickActions_actionArrow__t5VOB",roleSection:"QuickActions_roleSection__p3XkC",roleSectionTitle:"QuickActions_roleSectionTitle__3hd33",roleActions:"QuickActions_roleActions__ABEUD",roleAction:"QuickActions_roleAction__LD9qc",roleActionIcon:"QuickActions_roleActionIcon__XBYmS",adminSection:"QuickActions_adminSection__9CgaU",adminSectionTitle:"QuickActions_adminSectionTitle__CORLk",adminActions:"QuickActions_adminActions__Ermst",adminAction:"QuickActions_adminAction__Z_i5y",adminActionIcon:"QuickActions_adminActionIcon__D_rq9"}},6249:e=>{e.exports={recentBookingsContainer:"RecentBookings_recentBookingsContainer__Fzi_V",header:"RecentBookings_header__niTkD",headerLeft:"RecentBookings_headerLeft__j6eYM",sectionTitle:"RecentBookings_sectionTitle___vYuL",sectionSubtitle:"RecentBookings_sectionSubtitle__ldoQs",headerRight:"RecentBookings_headerRight__FOADn",viewAllButton:"RecentBookings_viewAllButton__9XLk8",bookingsContainer:"RecentBookings_bookingsContainer___NaRd",emptyState:"RecentBookings_emptyState__lZuHa",emptyIcon:"RecentBookings_emptyIcon__ggXGp",createButton:"RecentBookings_createButton__dRa5k",bookingsList:"RecentBookings_bookingsList__mLdMM",bookingCard:"RecentBookings_bookingCard__JkJQr",bookingHeader:"RecentBookings_bookingHeader__YCW2Y",customerInfo:"RecentBookings_customerInfo__rnHj_",customerAvatar:"RecentBookings_customerAvatar__HhJ4k",customerDetails:"RecentBookings_customerDetails__RB1Uk",customerName:"RecentBookings_customerName__yZper",bookingDate:"RecentBookings_bookingDate__6vh8O",statusBadge:"RecentBookings_statusBadge__cK9tp",statusIcon:"RecentBookings_statusIcon__H_4_C",statusText:"RecentBookings_statusText__YtiB5",bookingContent:"RecentBookings_bookingContent__zknkz",serviceInfo:"RecentBookings_serviceInfo__Nul5Q",serviceName:"RecentBookings_serviceName__Jd30G",artistName:"RecentBookings_artistName__zZ1Qj",bookingMeta:"RecentBookings_bookingMeta__mJ8Ng",metaItem:"RecentBookings_metaItem__ry2lT",metaIcon:"RecentBookings_metaIcon__GQLbI",metaText:"RecentBookings_metaText__J2Qse",bookingActions:"RecentBookings_bookingActions__tGYut",viewButton:"RecentBookings_viewButton__HZMID",confirmButton:"RecentBookings_confirmButton__03_BY",editButton:"RecentBookings_editButton__0W0pB",quickStats:"RecentBookings_quickStats__zLCO5",statItem:"RecentBookings_statItem__ZoaPp",statValue:"RecentBookings_statValue__hlRlY",statLabel:"RecentBookings_statLabel__0S2eC"}},6896:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>v,getStaticPaths:()=>h,getStaticProps:()=>_,reportWebVitals:()=>b,routeModule:()=>p,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>k,unstable_getStaticParams:()=>D,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>g});var i=s(7093),n=s(5244),o=s(1323),c=s(2899),r=s.n(c),d=s(6814),l=s(1261),m=e([d,l]);[d,l]=m.then?(await m)():m;let u=(0,o.l)(l,"default"),_=(0,o.l)(l,"getStaticProps"),h=(0,o.l)(l,"getStaticPaths"),v=(0,o.l)(l,"getServerSideProps"),x=(0,o.l)(l,"config"),b=(0,o.l)(l,"reportWebVitals"),g=(0,o.l)(l,"unstable_getStaticProps"),N=(0,o.l)(l,"unstable_getStaticPaths"),D=(0,o.l)(l,"unstable_getStaticParams"),j=(0,o.l)(l,"unstable_getServerProps"),k=(0,o.l)(l,"unstable_getServerSideProps"),p=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/dashboard",pathname:"/admin/dashboard",bundlePath:"",filename:""},components:{App:d.default,Document:r()},userland:l});a()}catch(e){a(e)}})},1583:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(997),i=s(4748),n=s.n(i);function o({activities:e,userRole:t}){let s=e=>{let t=new Date,s=new Date(e),a=Math.floor((t.getTime()-s.getTime())/6e4);if(a<1)return"Just now";if(a<60)return`${a}m ago`;let i=Math.floor(a/60);if(i<24)return`${i}h ago`;let n=Math.floor(i/24);return n<7?`${n}d ago`:s.toLocaleDateString("en-AU",{month:"short",day:"numeric"})},i=e=>{switch(e){case"booking":return"\uD83D\uDCC5";case"payment":return"\uD83D\uDCB0";case"customer":return"\uD83D\uDC64";case"system":return"⚙️";case"user":return"\uD83D\uDC68‍\uD83D\uDCBC";default:return"\uD83D\uDCDD"}},o=e=>{switch(e){case"booking":return"#3788d8";case"payment":return"#28a745";case"customer":return"#4ECDC4";case"system":return"#ffc107";case"user":return"#6f42c1";default:return"#6c757d"}},c=(e||[{id:"1",type:"booking",title:"New booking created",description:"Sarah Johnson booked Festival Face Paint for Jan 15",timestamp:"2024-01-14T10:30:00Z",user:"Emma Wilson",metadata:{bookingId:"BK001",amount:85}},{id:"2",type:"payment",title:"Payment received",description:"Payment of $120 received for booking BK002",timestamp:"2024-01-14T09:45:00Z",user:"System",metadata:{amount:120,method:"card"}},{id:"3",type:"customer",title:"New customer registered",description:"Mike Chen created a new account",timestamp:"2024-01-14T09:15:00Z",user:"Mike Chen",metadata:{customerId:"CUST123"}},{id:"4",type:"booking",title:"Booking confirmed",description:"Hair Braiding appointment confirmed for Jan 15",timestamp:"2024-01-14T08:30:00Z",user:"Lisa Brown",metadata:{bookingId:"BK002"}},{id:"5",type:"system",title:"Inventory alert",description:"Low stock alert for Glitter - Gold",timestamp:"2024-01-14T08:00:00Z",user:"System",metadata:{productId:"PROD456",stock:5}},{id:"6",type:"user",title:"Staff login",description:"Sophie Taylor logged into admin portal",timestamp:"2024-01-14T07:45:00Z",user:"Sophie Taylor",metadata:{role:"Artist"}},{id:"7",type:"booking",title:"Booking completed",description:"Glitter Art Design session completed",timestamp:"2024-01-13T16:00:00Z",user:"Sophie Taylor",metadata:{bookingId:"BK003",rating:5}},{id:"8",type:"payment",title:"Refund processed",description:"Refund of $200 processed for cancelled booking",timestamp:"2024-01-13T14:30:00Z",user:"Admin",metadata:{amount:200,bookingId:"BK004"}}]).filter(e=>"Artist"!==t&&"Braider"!==t||"booking"===e.type||"payment"===e.type&&"Current User"===e.user||"customer"===e.type);return(0,a.jsxs)("div",{className:n().activityFeedContainer,children:[(0,a.jsxs)("div",{className:n().header,children:[a.jsx("h2",{className:n().sectionTitle,children:"Recent Activity"}),a.jsx("p",{className:n().sectionSubtitle,children:"Latest system events"})]}),a.jsx("div",{className:n().activityList,children:0===c.length?(0,a.jsxs)("div",{className:n().emptyState,children:[a.jsx("div",{className:n().emptyIcon,children:"\uD83D\uDCDD"}),a.jsx("h3",{children:"No recent activity"}),a.jsx("p",{children:"Activity will appear here as events occur"})]}):c.map(e=>(0,a.jsxs)("div",{className:n().activityItem,children:[(0,a.jsxs)("div",{className:n().activityIconContainer,children:[a.jsx("div",{className:n().activityIcon,style:{backgroundColor:o(e.type)},children:i(e.type)}),a.jsx("div",{className:n().activityLine})]}),(0,a.jsxs)("div",{className:n().activityContent,children:[(0,a.jsxs)("div",{className:n().activityHeader,children:[a.jsx("div",{className:n().activityTitle,children:e.title}),a.jsx("div",{className:n().activityTime,children:s(e.timestamp)})]}),a.jsx("div",{className:n().activityDescription,children:e.description}),e.user&&(0,a.jsxs)("div",{className:n().activityUser,children:[a.jsx("span",{className:n().userIcon,children:"\uD83D\uDC64"}),a.jsx("span",{className:n().userName,children:e.user})]}),e.metadata&&(0,a.jsxs)("div",{className:n().activityMetadata,children:[e.metadata.amount&&(0,a.jsxs)("span",{className:n().metadataItem,children:["\uD83D\uDCB0 $",e.metadata.amount]}),e.metadata.bookingId&&(0,a.jsxs)("span",{className:n().metadataItem,children:["\uD83D\uDCCB ",e.metadata.bookingId]}),e.metadata.rating&&(0,a.jsxs)("span",{className:n().metadataItem,children:["⭐ ",e.metadata.rating,"/5"]})]})]})]},e.id))}),(0,a.jsxs)("div",{className:n().activitySummary,children:[(0,a.jsxs)("div",{className:n().summaryItem,children:[a.jsx("div",{className:n().summaryIcon,children:"\uD83D\uDCC5"}),(0,a.jsxs)("div",{className:n().summaryText,children:[a.jsx("div",{className:n().summaryValue,children:c.filter(e=>"booking"===e.type).length}),a.jsx("div",{className:n().summaryLabel,children:"Bookings"})]})]}),(0,a.jsxs)("div",{className:n().summaryItem,children:[a.jsx("div",{className:n().summaryIcon,children:"\uD83D\uDCB0"}),(0,a.jsxs)("div",{className:n().summaryText,children:[a.jsx("div",{className:n().summaryValue,children:c.filter(e=>"payment"===e.type).length}),a.jsx("div",{className:n().summaryLabel,children:"Payments"})]})]}),(0,a.jsxs)("div",{className:n().summaryItem,children:[a.jsx("div",{className:n().summaryIcon,children:"\uD83D\uDC64"}),(0,a.jsxs)("div",{className:n().summaryText,children:[a.jsx("div",{className:n().summaryValue,children:c.filter(e=>"customer"===e.type).length}),a.jsx("div",{className:n().summaryLabel,children:"Customers"})]})]})]})]})}},20:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(997),i=s(2998),n=s.n(i);function o({data:e}){var t;let s=e||{totalBookings:0,totalRevenue:0,activeCustomers:0,pendingBookings:0,completedBookings:0,cancelledBookings:0,averageBookingValue:0,monthlyGrowth:0},i=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),o=e=>e>0?"#28a745":e<0?"#dc3545":"#6c757d";return(0,a.jsxs)("div",{className:n().statsContainer,children:[a.jsx("h2",{className:n().sectionTitle,children:"Overview"}),(0,a.jsxs)("div",{className:n().statsGrid,children:[(0,a.jsxs)("div",{className:n().statCard,children:[(0,a.jsxs)("div",{className:n().statHeader,children:[a.jsx("div",{className:n().statIcon,children:"\uD83D\uDCB0"}),a.jsx("div",{className:n().statTrend,style:{color:o(s.monthlyGrowth)},children:(t=s.monthlyGrowth,`${t>=0?"+":""}${t.toFixed(1)}%`)})]}),a.jsx("div",{className:n().statValue,children:i(s.totalRevenue)}),a.jsx("div",{className:n().statLabel,children:"Total Revenue"}),a.jsx("div",{className:n().statSubtext,children:"This month"})]}),(0,a.jsxs)("div",{className:n().statCard,children:[(0,a.jsxs)("div",{className:n().statHeader,children:[a.jsx("div",{className:n().statIcon,children:"\uD83D\uDCC5"}),(0,a.jsxs)("div",{className:n().statBadge,children:[s.pendingBookings," pending"]})]}),a.jsx("div",{className:n().statValue,children:s.totalBookings.toLocaleString()}),a.jsx("div",{className:n().statLabel,children:"Total Bookings"}),a.jsx("div",{className:n().statSubtext,children:"All time"})]}),(0,a.jsxs)("div",{className:n().statCard,children:[(0,a.jsxs)("div",{className:n().statHeader,children:[a.jsx("div",{className:n().statIcon,children:"\uD83D\uDC65"}),(0,a.jsxs)("div",{className:n().statIndicator,children:[a.jsx("div",{className:n().statusDot}),"Active"]})]}),a.jsx("div",{className:n().statValue,children:s.activeCustomers.toLocaleString()}),a.jsx("div",{className:n().statLabel,children:"Active Customers"}),a.jsx("div",{className:n().statSubtext,children:"Last 30 days"})]}),(0,a.jsxs)("div",{className:n().statCard,children:[(0,a.jsxs)("div",{className:n().statHeader,children:[a.jsx("div",{className:n().statIcon,children:"\uD83D\uDC8E"}),a.jsx("div",{className:n().statTrend,style:{color:o(5.2)},children:"+5.2%"})]}),a.jsx("div",{className:n().statValue,children:i(s.averageBookingValue)}),a.jsx("div",{className:n().statLabel,children:"Avg. Booking Value"}),a.jsx("div",{className:n().statSubtext,children:"This month"})]})]}),(0,a.jsxs)("div",{className:n().breakdownSection,children:[a.jsx("h3",{className:n().breakdownTitle,children:"Booking Status"}),(0,a.jsxs)("div",{className:n().breakdownGrid,children:[(0,a.jsxs)("div",{className:n().breakdownCard,children:[a.jsx("div",{className:n().breakdownIcon,style:{background:"#28a745"},children:"✓"}),(0,a.jsxs)("div",{className:n().breakdownContent,children:[a.jsx("div",{className:n().breakdownValue,children:s.completedBookings}),a.jsx("div",{className:n().breakdownLabel,children:"Completed"})]})]}),(0,a.jsxs)("div",{className:n().breakdownCard,children:[a.jsx("div",{className:n().breakdownIcon,style:{background:"#ffc107"},children:"⏳"}),(0,a.jsxs)("div",{className:n().breakdownContent,children:[a.jsx("div",{className:n().breakdownValue,children:s.pendingBookings}),a.jsx("div",{className:n().breakdownLabel,children:"Pending"})]})]}),(0,a.jsxs)("div",{className:n().breakdownCard,children:[a.jsx("div",{className:n().breakdownIcon,style:{background:"#dc3545"},children:"✕"}),(0,a.jsxs)("div",{className:n().breakdownContent,children:[a.jsx("div",{className:n().breakdownValue,children:s.cancelledBookings}),a.jsx("div",{className:n().breakdownLabel,children:"Cancelled"})]})]})]})]})]})}},7021:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var a=s(997),i=s(1664),n=s.n(i),o=s(9889),c=s.n(o);let r=[{id:"new-booking",title:"New Booking",description:"Create a new customer booking",icon:"\uD83D\uDCC5",href:"/admin/bookings/new",color:"#3788d8",roles:["DEV","Admin","Artist","Braider"]},{id:"new-customer",title:"Add Customer",description:"Register a new customer",icon:"\uD83D\uDC64",href:"/admin/customers/new",color:"#28a745",roles:["DEV","Admin"]},{id:"new-service",title:"Add Service",description:"Create a new service offering",icon:"✨",href:"/admin/services/new",color:"#4ECDC4",roles:["DEV","Admin"]},{id:"new-product",title:"Add Product",description:"Add a new product to inventory",icon:"\uD83D\uDECD️",href:"/admin/products/new",color:"#fd7e14",roles:["DEV","Admin"]},{id:"view-calendar",title:"Calendar",description:"View booking calendar",icon:"\uD83D\uDCC6",href:"/admin/calendar",color:"#6f42c1",roles:["DEV","Admin","Artist","Braider"]},{id:"reports",title:"Reports",description:"View business reports",icon:"\uD83D\uDCCA",href:"/admin/reports",color:"#e83e8c",roles:["DEV","Admin"]},{id:"inventory",title:"Inventory",description:"Manage product inventory",icon:"\uD83D\uDCE6",href:"/admin/inventory",color:"#20c997",roles:["DEV","Admin"]},{id:"settings",title:"Settings",description:"Configure system settings",icon:"⚙️",href:"/admin/settings",color:"#6c757d",roles:["DEV","Admin"]}];function d({userRole:e}){let t=t=>t.includes(e),s=r.filter(e=>t(e.roles));return(0,a.jsxs)("div",{className:c().quickActionsContainer,children:[(0,a.jsxs)("div",{className:c().header,children:[a.jsx("h2",{className:c().sectionTitle,children:"Quick Actions"}),a.jsx("p",{className:c().sectionSubtitle,children:"Common tasks and shortcuts"})]}),a.jsx("div",{className:c().actionsGrid,children:s.map(e=>(0,a.jsxs)(n(),{href:e.href,className:c().actionCard,style:{"--action-color":e.color},children:[a.jsx("div",{className:c().actionIcon,children:e.icon}),(0,a.jsxs)("div",{className:c().actionContent,children:[a.jsx("h3",{className:c().actionTitle,children:e.title}),a.jsx("p",{className:c().actionDescription,children:e.description})]}),a.jsx("div",{className:c().actionArrow,children:"→"})]},e.id))}),("Artist"===e||"Braider"===e)&&(0,a.jsxs)("div",{className:c().roleSection,children:[a.jsx("h3",{className:c().roleSectionTitle,children:"Your Tools"}),(0,a.jsxs)("div",{className:c().roleActions,children:[(0,a.jsxs)(n(),{href:"/admin/my-bookings",className:c().roleAction,children:[a.jsx("span",{className:c().roleActionIcon,children:"\uD83D\uDCCB"}),a.jsx("span",{children:"My Bookings"})]}),(0,a.jsxs)(n(),{href:"/admin/my-schedule",className:c().roleAction,children:[a.jsx("span",{className:c().roleActionIcon,children:"\uD83D\uDDD3️"}),a.jsx("span",{children:"My Schedule"})]}),(0,a.jsxs)(n(),{href:"/admin/my-earnings",className:c().roleAction,children:[a.jsx("span",{className:c().roleActionIcon,children:"\uD83D\uDCB0"}),a.jsx("span",{children:"My Earnings"})]}),(0,a.jsxs)(n(),{href:"/admin/my-profile",className:c().roleAction,children:[a.jsx("span",{className:c().roleActionIcon,children:"\uD83D\uDC68‍\uD83C\uDFA8"}),a.jsx("span",{children:"My Profile"})]})]})]}),("DEV"===e||"Admin"===e)&&(0,a.jsxs)("div",{className:c().adminSection,children:[a.jsx("h3",{className:c().adminSectionTitle,children:"Admin Tools"}),(0,a.jsxs)("div",{className:c().adminActions,children:[(0,a.jsxs)("button",{className:c().adminAction,children:[a.jsx("span",{className:c().adminActionIcon,children:"\uD83D\uDD04"}),a.jsx("span",{children:"Sync Data"})]}),(0,a.jsxs)("button",{className:c().adminAction,children:[a.jsx("span",{className:c().adminActionIcon,children:"\uD83D\uDCE4"}),a.jsx("span",{children:"Export Data"})]}),(0,a.jsxs)("button",{className:c().adminAction,children:[a.jsx("span",{className:c().adminActionIcon,children:"\uD83D\uDD27"}),a.jsx("span",{children:"System Check"})]}),(0,a.jsxs)("button",{className:c().adminAction,children:[a.jsx("span",{className:c().adminActionIcon,children:"\uD83D\uDCCA"}),a.jsx("span",{children:"Analytics"})]})]})]})]})}},5923:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(997),i=s(1664),n=s.n(i),o=s(6249),c=s.n(o);function r({bookings:e,userRole:t}){let s=e||[{id:"1",customerName:"Sarah Johnson",serviceName:"Festival Face Paint",artistName:"Emma Wilson",scheduledDate:"2024-01-15T14:00:00Z",status:"confirmed",totalAmount:85,duration:60},{id:"2",customerName:"Mike Chen",serviceName:"Hair Braiding - Complex",artistName:"Lisa Brown",scheduledDate:"2024-01-15T16:30:00Z",status:"pending",totalAmount:120,duration:90},{id:"3",customerName:"Emma Davis",serviceName:"Glitter Art Design",artistName:"Sophie Taylor",scheduledDate:"2024-01-14T13:00:00Z",status:"completed",totalAmount:65,duration:45},{id:"4",customerName:"James Wilson",serviceName:"Body Art - Medium",artistName:"Emma Wilson",scheduledDate:"2024-01-14T10:00:00Z",status:"completed",totalAmount:150,duration:120},{id:"5",customerName:"Anna Martinez",serviceName:"Special Event Package",artistName:"Lisa Brown",scheduledDate:"2024-01-13T15:00:00Z",status:"cancelled",totalAmount:200,duration:180}],i=e=>new Date(e).toLocaleDateString("en-AU",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),o=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),r=e=>{switch(e){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"completed":return"#17a2b8";case"cancelled":return"#dc3545";default:return"#6c757d"}},d=e=>{switch(e){case"confirmed":return"✓";case"pending":return"⏳";case"completed":return"\uD83C\uDF89";case"cancelled":return"✕";default:return"?"}};return(0,a.jsxs)("div",{className:c().recentBookingsContainer,children:[(0,a.jsxs)("div",{className:c().header,children:[(0,a.jsxs)("div",{className:c().headerLeft,children:[a.jsx("h2",{className:c().sectionTitle,children:"Recent Bookings"}),a.jsx("p",{className:c().sectionSubtitle,children:"Latest booking activity"})]}),a.jsx("div",{className:c().headerRight,children:a.jsx(n(),{href:"/admin/bookings",className:c().viewAllButton,children:"View All"})})]}),a.jsx("div",{className:c().bookingsContainer,children:0===s.length?(0,a.jsxs)("div",{className:c().emptyState,children:[a.jsx("div",{className:c().emptyIcon,children:"\uD83D\uDCC5"}),a.jsx("h3",{children:"No recent bookings"}),a.jsx("p",{children:"New bookings will appear here"}),a.jsx(n(),{href:"/admin/bookings/new",className:c().createButton,children:"Create New Booking"})]}):a.jsx("div",{className:c().bookingsList,children:s.map(e=>(0,a.jsxs)("div",{className:c().bookingCard,children:[(0,a.jsxs)("div",{className:c().bookingHeader,children:[(0,a.jsxs)("div",{className:c().customerInfo,children:[a.jsx("div",{className:c().customerAvatar,children:e.customerName.split(" ").map(e=>e[0]).join("")}),(0,a.jsxs)("div",{className:c().customerDetails,children:[a.jsx("div",{className:c().customerName,children:e.customerName}),a.jsx("div",{className:c().bookingDate,children:i(e.scheduledDate)})]})]}),(0,a.jsxs)("div",{className:c().statusBadge,style:{backgroundColor:r(e.status)},children:[a.jsx("span",{className:c().statusIcon,children:d(e.status)}),a.jsx("span",{className:c().statusText,children:e.status})]})]}),(0,a.jsxs)("div",{className:c().bookingContent,children:[(0,a.jsxs)("div",{className:c().serviceInfo,children:[a.jsx("div",{className:c().serviceName,children:e.serviceName}),e.artistName&&(0,a.jsxs)("div",{className:c().artistName,children:["with ",e.artistName]})]}),(0,a.jsxs)("div",{className:c().bookingMeta,children:[(0,a.jsxs)("div",{className:c().metaItem,children:[a.jsx("span",{className:c().metaIcon,children:"⏱️"}),(0,a.jsxs)("span",{className:c().metaText,children:[e.duration," min"]})]}),(0,a.jsxs)("div",{className:c().metaItem,children:[a.jsx("span",{className:c().metaIcon,children:"\uD83D\uDCB0"}),a.jsx("span",{className:c().metaText,children:o(e.totalAmount)})]})]})]}),(0,a.jsxs)("div",{className:c().bookingActions,children:[a.jsx(n(),{href:`/admin/bookings/${e.id}`,className:c().viewButton,children:"View Details"}),"pending"===e.status&&a.jsx("button",{className:c().confirmButton,children:"Confirm"}),("DEV"===t||"Admin"===t)&&a.jsx("button",{className:c().editButton,children:"Edit"})]})]},e.id))})}),(0,a.jsxs)("div",{className:c().quickStats,children:[(0,a.jsxs)("div",{className:c().statItem,children:[a.jsx("div",{className:c().statValue,children:s.filter(e=>"pending"===e.status).length}),a.jsx("div",{className:c().statLabel,children:"Pending"})]}),(0,a.jsxs)("div",{className:c().statItem,children:[a.jsx("div",{className:c().statValue,children:s.filter(e=>"confirmed"===e.status).length}),a.jsx("div",{className:c().statLabel,children:"Confirmed"})]}),(0,a.jsxs)("div",{className:c().statItem,children:[a.jsx("div",{className:c().statValue,children:o(s.reduce((e,t)=>e+t.totalAmount,0))}),a.jsx("div",{className:c().statLabel,children:"Total Value"})]})]})]})}},1261:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>b});var i=s(997),n=s(6689),o=s(968),c=s.n(o),r=s(4845),d=s(20),l=s(5923),m=s(7021),u=s(1583),_=s(8568),h=s(9422),v=s.n(h),x=e([r]);function b(){let{user:e}=(0,_.a)(),[t,s]=(0,n.useState)(null),[a,o]=(0,n.useState)(!0),[h,x]=(0,n.useState)(null),b=async()=>{try{o(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/dashboard",{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to load dashboard data");let a=await t.json();s(a)}catch(e){console.error("Dashboard error:",e),x(e instanceof Error?e.message:"Failed to load dashboard")}finally{o(!1)}};return a?i.jsx(r.Z,{children:(0,i.jsxs)("div",{className:v().loading,children:[i.jsx("div",{className:v().spinner}),i.jsx("p",{children:"Loading dashboard..."})]})}):h?i.jsx(r.Z,{children:(0,i.jsxs)("div",{className:v().error,children:[i.jsx("h2",{children:"Error Loading Dashboard"}),i.jsx("p",{children:h}),i.jsx("button",{onClick:b,className:v().retryButton,children:"Try Again"})]})}):(0,i.jsxs)(r.Z,{children:[i.jsx(c(),{children:i.jsx("title",{children:"Admin Dashboard - Ocean Soul Sparkles"})}),(0,i.jsxs)("div",{className:v().dashboard,children:[(0,i.jsxs)("div",{className:v().header,children:[(0,i.jsxs)("h1",{children:["Welcome back, ",e?.firstName,"!"]}),i.jsx("p",{children:"Here's what's happening with your business today."})]}),"        ",t&&(0,i.jsxs)(i.Fragment,{children:[i.jsx(d.Z,{data:t.stats}),(0,i.jsxs)("div",{className:v().grid,children:[(0,i.jsxs)("div",{className:v().leftColumn,children:[i.jsx(l.Z,{bookings:t.recentBookings,userRole:e?.role||"Admin"}),i.jsx(m.Z,{userRole:e?.role||"Admin"})]}),i.jsx("div",{className:v().rightColumn,children:i.jsx(u.Z,{activities:t.recentActivity,userRole:e?.role||"Admin"})})]})]})]})]})}r=(x.then?(await x)():x)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(6896));module.exports=a})();