/* Admin Layout Styles */
.adminLayout {
  display: flex;
  min-height: 100vh;
  background: var(--admin-bg-secondary);
}

.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
  transition: margin-left var(--admin-transition-normal);
}

.mainContent.sidebarCollapsed {
  margin-left: 70px;
}

.pageContent {
  flex: 1;
  padding: var(--admin-spacing-lg);
  overflow-y: auto;
}

.adminFooter {
  background: var(--admin-bg-primary);
  border-top: 1px solid var(--admin-border-light);
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  margin-top: auto;
}

.footerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.footerLeft {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  color: var(--admin-gray);
  font-size: 0.85rem;
}

.version {
  background: var(--admin-bg-tertiary);
  padding: 2px 8px;
  border-radius: var(--admin-radius-sm);
  font-weight: 600;
  font-size: 0.75rem;
}

.footerRight {
  display: flex;
  gap: var(--admin-spacing-lg);
}

.footerLink {
  color: var(--admin-gray);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color var(--admin-transition-normal);
}

.footerLink:hover {
  color: var(--admin-primary);
}

.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--admin-z-modal-backdrop);
}

.securityBanner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--admin-primary);
  color: white;
  padding: var(--admin-spacing-xs) var(--admin-spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  font-size: 0.8rem;
  font-weight: 500;
  z-index: var(--admin-z-fixed);
}

.securityIcon {
  font-size: 0.9rem;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--admin-bg-secondary);
  color: var(--admin-gray);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--admin-border-light);
  border-top: 4px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--admin-spacing-md);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .adminLayout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    overflow-x: hidden;
  }

  .mainContent {
    flex: 1;
    margin-left: 0 !important;
    padding-bottom: 80px; /* Account for bottom navigation */
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .mainContent.sidebarCollapsed {
    margin-left: 0 !important;
  }

  .pageContent {
    padding: var(--admin-spacing-md);
    padding-bottom: var(--admin-spacing-lg); /* Extra padding for bottom nav */
    max-width: 100%;
    overflow-x: hidden;
  }

  .footerContent {
    flex-direction: column;
    gap: var(--admin-spacing-sm);
    text-align: center;
  }

  .footerRight {
    gap: var(--admin-spacing-md);
  }

  .securityBanner {
    position: relative;
    margin-top: var(--admin-spacing-md);
    margin-bottom: 80px; /* Account for bottom navigation */
  }

  /* Hide desktop sidebar on mobile */
  .adminLayout .sidebar {
    display: none !important;
  }

  /* Ensure all content respects mobile viewport */
  .adminLayout *,
  .mainContent *,
  .pageContent * {
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Mobile Bottom Navigation */
.mobileBottomNav {
  display: none; /* Hidden on desktop */
}

@media (max-width: 768px) {
  .mobileBottomNav {
    display: block !important;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }
}

/* Mobile overlay for hamburger menu */
.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

@media (max-width: 768px) {
  .mobileOverlay {
    display: block;
  }
}

@media (max-width: 480px) {
  .pageContent {
    padding: var(--admin-spacing-sm);
  }

  .footerLeft {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .securityBanner {
    font-size: 0.75rem;
    padding: var(--admin-spacing-xs);
  }
}

/* Debug styles for mobile development */
.debugMobile {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  display: none;
}

@media (max-width: 768px) {
  .debugMobile {
    display: block;
  }
}
