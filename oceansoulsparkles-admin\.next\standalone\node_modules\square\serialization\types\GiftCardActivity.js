"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiftCardActivity = void 0;
const core = __importStar(require("../../core"));
const GiftCardActivityType_1 = require("./GiftCardActivityType");
const Money_1 = require("./Money");
const GiftCardActivityLoad_1 = require("./GiftCardActivityLoad");
const GiftCardActivityActivate_1 = require("./GiftCardActivityActivate");
const GiftCardActivityRedeem_1 = require("./GiftCardActivityRedeem");
const GiftCardActivityClearBalance_1 = require("./GiftCardActivityClearBalance");
const GiftCardActivityDeactivate_1 = require("./GiftCardActivityDeactivate");
const GiftCardActivityAdjustIncrement_1 = require("./GiftCardActivityAdjustIncrement");
const GiftCardActivityAdjustDecrement_1 = require("./GiftCardActivityAdjustDecrement");
const GiftCardActivityRefund_1 = require("./GiftCardActivityRefund");
const GiftCardActivityUnlinkedActivityRefund_1 = require("./GiftCardActivityUnlinkedActivityRefund");
const GiftCardActivityImport_1 = require("./GiftCardActivityImport");
const GiftCardActivityBlock_1 = require("./GiftCardActivityBlock");
const GiftCardActivityUnblock_1 = require("./GiftCardActivityUnblock");
const GiftCardActivityImportReversal_1 = require("./GiftCardActivityImportReversal");
const GiftCardActivityTransferBalanceTo_1 = require("./GiftCardActivityTransferBalanceTo");
const GiftCardActivityTransferBalanceFrom_1 = require("./GiftCardActivityTransferBalanceFrom");
exports.GiftCardActivity = core.serialization.object({
    id: core.serialization.string().optional(),
    type: GiftCardActivityType_1.GiftCardActivityType,
    locationId: core.serialization.property("location_id", core.serialization.string()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    giftCardId: core.serialization.property("gift_card_id", core.serialization.string().optionalNullable()),
    giftCardGan: core.serialization.property("gift_card_gan", core.serialization.string().optionalNullable()),
    giftCardBalanceMoney: core.serialization.property("gift_card_balance_money", Money_1.Money.optional()),
    loadActivityDetails: core.serialization.property("load_activity_details", GiftCardActivityLoad_1.GiftCardActivityLoad.optional()),
    activateActivityDetails: core.serialization.property("activate_activity_details", GiftCardActivityActivate_1.GiftCardActivityActivate.optional()),
    redeemActivityDetails: core.serialization.property("redeem_activity_details", GiftCardActivityRedeem_1.GiftCardActivityRedeem.optional()),
    clearBalanceActivityDetails: core.serialization.property("clear_balance_activity_details", GiftCardActivityClearBalance_1.GiftCardActivityClearBalance.optional()),
    deactivateActivityDetails: core.serialization.property("deactivate_activity_details", GiftCardActivityDeactivate_1.GiftCardActivityDeactivate.optional()),
    adjustIncrementActivityDetails: core.serialization.property("adjust_increment_activity_details", GiftCardActivityAdjustIncrement_1.GiftCardActivityAdjustIncrement.optional()),
    adjustDecrementActivityDetails: core.serialization.property("adjust_decrement_activity_details", GiftCardActivityAdjustDecrement_1.GiftCardActivityAdjustDecrement.optional()),
    refundActivityDetails: core.serialization.property("refund_activity_details", GiftCardActivityRefund_1.GiftCardActivityRefund.optional()),
    unlinkedActivityRefundActivityDetails: core.serialization.property("unlinked_activity_refund_activity_details", GiftCardActivityUnlinkedActivityRefund_1.GiftCardActivityUnlinkedActivityRefund.optional()),
    importActivityDetails: core.serialization.property("import_activity_details", GiftCardActivityImport_1.GiftCardActivityImport.optional()),
    blockActivityDetails: core.serialization.property("block_activity_details", GiftCardActivityBlock_1.GiftCardActivityBlock.optional()),
    unblockActivityDetails: core.serialization.property("unblock_activity_details", GiftCardActivityUnblock_1.GiftCardActivityUnblock.optional()),
    importReversalActivityDetails: core.serialization.property("import_reversal_activity_details", GiftCardActivityImportReversal_1.GiftCardActivityImportReversal.optional()),
    transferBalanceToActivityDetails: core.serialization.property("transfer_balance_to_activity_details", GiftCardActivityTransferBalanceTo_1.GiftCardActivityTransferBalanceTo.optional()),
    transferBalanceFromActivityDetails: core.serialization.property("transfer_balance_from_activity_details", GiftCardActivityTransferBalanceFrom_1.GiftCardActivityTransferBalanceFrom.optional()),
});
