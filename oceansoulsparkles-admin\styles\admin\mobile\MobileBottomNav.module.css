/**
 * Ocean Soul Sparkles Admin - Mobile Bottom Navigation Styles
 * Mobile-optimized bottom navigation for quick access to main admin sections
 */

.mobileBottomNav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--admin-card-background, #ffffff);
  border-top: 1px solid var(--admin-border, #e0e0e0);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.navContainer {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 8px 0;
  max-width: 100%;
  margin: 0 auto;
}

.navItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  text-decoration: none;
  color: var(--admin-text-secondary, #666666);
  transition: all 0.2s ease;
  border-radius: 12px;
  position: relative;
  min-width: 60px;
  min-height: 56px;
  cursor: pointer;
}

.navItem:hover {
  background: var(--admin-hover-background, #f5f5f5);
  color: var(--admin-text-primary, #1a1a1a);
}

.navItem:active {
  transform: scale(0.95);
  background: var(--admin-active-background, #e0e0e0);
}

.navItem.active {
  color: var(--admin-primary, #16213e);
  background: rgba(22, 33, 62, 0.1);
}

.navIcon {
  font-size: 1.25rem;
  margin-bottom: 2px;
  transition: transform 0.2s ease;
}

.navItem.active .navIcon {
  transform: scale(1.1);
}

.navLabel {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.activeIndicator {
  position: absolute;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--admin-primary, #16213e);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.safeAreaPadding {
  height: env(safe-area-inset-bottom, 0);
  background: var(--admin-card-background, #ffffff);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.2);
  }
}

/* Mobile-specific optimizations */
@media (max-width: 480px) {
  .navContainer {
    padding: 6px 0;
  }

  .navItem {
    padding: 6px 8px;
    min-width: 50px;
    min-height: 52px;
  }

  .navIcon {
    font-size: 1.125rem;
  }

  .navLabel {
    font-size: 0.7rem;
    max-width: 50px;
  }
}

/* Very small screens */
@media (max-width: 320px) {
  .navItem {
    padding: 4px 6px;
    min-width: 45px;
    min-height: 48px;
  }

  .navIcon {
    font-size: 1rem;
  }

  .navLabel {
    font-size: 0.65rem;
    max-width: 45px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .navContainer {
    padding: 4px 0;
  }

  .navItem {
    min-height: 44px;
    padding: 4px 8px;
  }

  .navIcon {
    font-size: 1rem;
    margin-bottom: 1px;
  }

  .navLabel {
    font-size: 0.7rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobileBottomNav {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-top-color: var(--admin-border-dark, #404040);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
  }

  .navItem {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .navItem:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .navItem:active {
    background: var(--admin-active-background-dark, #4a4a4a);
  }

  .navItem.active {
    color: var(--admin-primary, #16213e);
    background: rgba(22, 33, 62, 0.2);
  }

  .safeAreaPadding {
    background: var(--admin-card-background-dark, #2a2a2a);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobileBottomNav {
    border-top-width: 2px;
    border-top-color: var(--admin-border, #000000);
  }

  .navItem.active {
    background: var(--admin-primary, #16213e);
    color: white;
  }

  .activeIndicator {
    width: 6px;
    height: 6px;
    background: var(--admin-primary, #16213e);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .navItem {
    transition: color 0.1s ease;
  }

  .navItem:active {
    transform: none;
  }

  .navIcon {
    transition: none;
  }

  .navItem.active .navIcon {
    transform: none;
  }

  .activeIndicator {
    animation: none;
  }
}

/* Focus styles for accessibility */
.navItem:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: 2px;
}

.navItem:focus:not(:focus-visible) {
  outline: none;
}

/* Print styles */
@media print {
  .mobileBottomNav {
    display: none;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  .mobileBottomNav {
    display: none; /* Hide on tablet sizes where sidebar is visible */
  }
}

/* Support for devices with notches */
@supports (padding: max(0px)) {
  .safeAreaPadding {
    height: max(env(safe-area-inset-bottom, 0), 8px);
  }
}

/* Hover effects for touch devices */
@media (hover: none) {
  .navItem:hover {
    background: transparent;
    color: var(--admin-text-secondary, #666666);
  }

  .navItem.active:hover {
    color: var(--admin-primary, #16213e);
    background: rgba(22, 33, 62, 0.1);
  }
}

/* Performance optimizations */
.mobileBottomNav {
  will-change: transform;
  transform: translateZ(0);
}

.navItem {
  will-change: transform, background-color;
  transform: translateZ(0);
}

/* Badge support for notifications */
.navItem[data-badge]::after {
  content: attr(data-badge);
  position: absolute;
  top: 2px;
  right: 8px;
  background: #f44336;
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.navItem[data-badge=""]::after {
  content: "";
  width: 8px;
  height: 8px;
  padding: 0;
  min-width: 8px;
}
