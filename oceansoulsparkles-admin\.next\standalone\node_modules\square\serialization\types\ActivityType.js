"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityType = void 0;
const core = __importStar(require("../../core"));
exports.ActivityType = core.serialization.enum_([
    "ADJUSTMENT",
    "APP_FEE_REFUND",
    "APP_FEE_REVENUE",
    "AUTOMATIC_SAVINGS",
    "AUTOMATIC_SAVINGS_REVERSED",
    "CHARGE",
    "DEPOSIT_FEE",
    "DEPOSIT_FEE_REVERSED",
    "DISPUTE",
    "ESCHEATMENT",
    "FEE",
    "FREE_PROCESSING",
    "HOLD_ADJUSTMENT",
    "INITIAL_BALANCE_CHANGE",
    "MONEY_TRANSFER",
    "MONEY_TRANSFER_REVERSAL",
    "OPEN_DISPUTE",
    "OTHER",
    "OTHER_ADJUSTMENT",
    "PAID_SERVICE_FEE",
    "PAID_SERVICE_FEE_REFUND",
    "REDEMPTION_CODE",
    "REFUND",
    "RELEASE_ADJUSTMENT",
    "RESERVE_HOLD",
    "RESERVE_RELEASE",
    "RETURNED_PAYOUT",
    "SQUARE_CAPITAL_PAYMENT",
    "SQUARE_CAPITAL_REVERSED_PAYMENT",
    "SUBSCRIPTION_FEE",
    "SUBSCRIPTION_FEE_PAID_REFUND",
    "SUBSCRIPTION_FEE_REFUND",
    "TAX_ON_FEE",
    "THIRD_PARTY_FEE",
    "THIRD_PARTY_FEE_REFUND",
    "PAYOUT",
    "AUTOMATIC_BITCOIN_CONVERSIONS",
    "AUTOMATIC_BITCOIN_CONVERSIONS_REVERSED",
    "CREDIT_CARD_REPAYMENT",
    "CREDIT_CARD_REPAYMENT_REVERSED",
    "LOCAL_OFFERS_CASHBACK",
    "LOCAL_OFFERS_FEE",
    "PERCENTAGE_PROCESSING_ENROLLMENT",
    "PERCENTAGE_PROCESSING_DEACTIVATION",
    "PERCENTAGE_PROCESSING_REPAYMENT",
    "PERCENTAGE_PROCESSING_REPAYMENT_REVERSED",
    "PROCESSING_FEE",
    "PROCESSING_FEE_REFUND",
    "UNDO_PROCESSING_FEE_REFUND",
    "GIFT_CARD_LOAD_FEE",
    "GIFT_CARD_LOAD_FEE_REFUND",
    "UNDO_GIFT_CARD_LOAD_FEE_REFUND",
    "BALANCE_FOLDERS_TRANSFER",
    "BALANCE_FOLDERS_TRANSFER_REVERSED",
    "GIFT_CARD_POOL_TRANSFER",
    "GIFT_CARD_POOL_TRANSFER_REVERSED",
    "SQUARE_PAYROLL_TRANSFER",
    "SQUARE_PAYROLL_TRANSFER_REVERSED",
]);
