"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogCustomAttributeDefinition = void 0;
const core = __importStar(require("../../core"));
const CatalogCustomAttributeDefinitionType_1 = require("./CatalogCustomAttributeDefinitionType");
const SourceApplication_1 = require("./SourceApplication");
const CatalogObjectType_1 = require("./CatalogObjectType");
const CatalogCustomAttributeDefinitionSellerVisibility_1 = require("./CatalogCustomAttributeDefinitionSellerVisibility");
const CatalogCustomAttributeDefinitionAppVisibility_1 = require("./CatalogCustomAttributeDefinitionAppVisibility");
const CatalogCustomAttributeDefinitionStringConfig_1 = require("./CatalogCustomAttributeDefinitionStringConfig");
const CatalogCustomAttributeDefinitionNumberConfig_1 = require("./CatalogCustomAttributeDefinitionNumberConfig");
const CatalogCustomAttributeDefinitionSelectionConfig_1 = require("./CatalogCustomAttributeDefinitionSelectionConfig");
exports.CatalogCustomAttributeDefinition = core.serialization.object({
    type: CatalogCustomAttributeDefinitionType_1.CatalogCustomAttributeDefinitionType,
    name: core.serialization.string(),
    description: core.serialization.string().optionalNullable(),
    sourceApplication: core.serialization.property("source_application", SourceApplication_1.SourceApplication.optional()),
    allowedObjectTypes: core.serialization.property("allowed_object_types", core.serialization.list(CatalogObjectType_1.CatalogObjectType)),
    sellerVisibility: core.serialization.property("seller_visibility", CatalogCustomAttributeDefinitionSellerVisibility_1.CatalogCustomAttributeDefinitionSellerVisibility.optional()),
    appVisibility: core.serialization.property("app_visibility", CatalogCustomAttributeDefinitionAppVisibility_1.CatalogCustomAttributeDefinitionAppVisibility.optional()),
    stringConfig: core.serialization.property("string_config", CatalogCustomAttributeDefinitionStringConfig_1.CatalogCustomAttributeDefinitionStringConfig.optional()),
    numberConfig: core.serialization.property("number_config", CatalogCustomAttributeDefinitionNumberConfig_1.CatalogCustomAttributeDefinitionNumberConfig.optional()),
    selectionConfig: core.serialization.property("selection_config", CatalogCustomAttributeDefinitionSelectionConfig_1.CatalogCustomAttributeDefinitionSelectionConfig.optional()),
    customAttributeUsageCount: core.serialization.property("custom_attribute_usage_count", core.serialization.number().optional()),
    key: core.serialization.string().optionalNullable(),
});
