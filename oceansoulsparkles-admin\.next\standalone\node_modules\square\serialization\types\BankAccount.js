"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankAccount = void 0;
const core = __importStar(require("../../core"));
const Country_1 = require("./Country");
const Currency_1 = require("./Currency");
const BankAccountType_1 = require("./BankAccountType");
const BankAccountStatus_1 = require("./BankAccountStatus");
exports.BankAccount = core.serialization.object({
    id: core.serialization.string(),
    accountNumberSuffix: core.serialization.property("account_number_suffix", core.serialization.string()),
    country: Country_1.Country,
    currency: Currency_1.Currency,
    accountType: core.serialization.property("account_type", BankAccountType_1.BankAccountType),
    holderName: core.serialization.property("holder_name", core.serialization.string()),
    primaryBankIdentificationNumber: core.serialization.property("primary_bank_identification_number", core.serialization.string()),
    secondaryBankIdentificationNumber: core.serialization.property("secondary_bank_identification_number", core.serialization.string().optionalNullable()),
    debitMandateReferenceId: core.serialization.property("debit_mandate_reference_id", core.serialization.string().optionalNullable()),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optionalNullable()),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    status: BankAccountStatus_1.BankAccountStatus,
    creditable: core.serialization.boolean(),
    debitable: core.serialization.boolean(),
    fingerprint: core.serialization.string().optionalNullable(),
    version: core.serialization.number().optional(),
    bankName: core.serialization.property("bank_name", core.serialization.string().optionalNullable()),
});
