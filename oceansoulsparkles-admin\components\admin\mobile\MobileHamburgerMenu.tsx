/**
 * Ocean Soul Sparkles Admin - Mobile Hamburger Menu Component
 * Slide-out mobile menu for additional navigation options
 */

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/MobileHamburgerMenu.module.css';

interface MobileHamburgerMenuProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: string;
  userName: string;
}

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
  section?: string;
}

export default function MobileHamburgerMenu({
  isOpen,
  onClose,
  userRole,
  userName
}: MobileHamburgerMenuProps) {
  const router = useRouter();

  const menuItems: MenuItem[] = [
    // Core Features
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      href: '/admin/dashboard',
      roles: ['Admin', 'Manager', 'Staff'],
      section: 'Core'
    },
    {
      id: 'pos',
      label: 'Point of Sale',
      icon: '💳',
      href: '/admin/pos',
      roles: ['Admin', 'Manager', 'Staff'],
      section: 'Core'
    },
    {
      id: 'bookings',
      label: 'Bookings',
      icon: '📅',
      href: '/admin/bookings',
      roles: ['Admin', 'Manager', 'Staff'],
      section: 'Core'
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: '👥',
      href: '/admin/customers',
      roles: ['Admin', 'Manager', 'Staff'],
      section: 'Core'
    },
    
    // Management
    {
      id: 'services',
      label: 'Services',
      icon: '✂️',
      href: '/admin/services',
      roles: ['Admin', 'Manager'],
      section: 'Management'
    },
    {
      id: 'products',
      label: 'Products',
      icon: '🛍️',
      href: '/admin/products',
      roles: ['Admin', 'Manager'],
      section: 'Management'
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: '📦',
      href: '/admin/inventory',
      roles: ['Admin', 'Manager'],
      section: 'Management'
    },
    {
      id: 'staff',
      label: 'Staff Management',
      icon: '👨‍💼',
      href: '/admin/staff',
      roles: ['Admin', 'Manager'],
      section: 'Management'
    },
    
    // Analytics & Reports
    {
      id: 'analytics',
      label: 'Analytics',
      icon: '📈',
      href: '/admin/analytics',
      roles: ['Admin', 'Manager'],
      section: 'Analytics'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: '📋',
      href: '/admin/reports',
      roles: ['Admin', 'Manager'],
      section: 'Analytics'
    },
    
    // Settings
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      href: '/admin/settings',
      roles: ['Admin'],
      section: 'Settings'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: '👤',
      href: '/admin/profile',
      roles: ['Admin', 'Manager', 'Staff'],
      section: 'Settings'
    }
  ];

  // Close menu on route change
  useEffect(() => {
    const handleRouteChange = () => {
      onClose();
    };

    router.events.on('routeChangeStart', handleRouteChange);
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [router.events, onClose]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleMenuItemClick = (item: MenuItem) => {
    HapticFeedback.light();
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      HapticFeedback.light();
      onClose();
    }
  };

  const isActive = (href: string): boolean => {
    if (href === '/admin/dashboard') {
      return router.pathname === '/admin/dashboard' || router.pathname === '/admin';
    }
    return router.pathname.startsWith(href);
  };

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(userRole)
  );

  const groupedItems = filteredMenuItems.reduce((groups, item) => {
    const section = item.section || 'Other';
    if (!groups[section]) {
      groups[section] = [];
    }
    groups[section].push(item);
    return groups;
  }, {} as Record<string, MenuItem[]>);

  if (!isOpen) return null;

  return (
    <div 
      className={`${styles.menuOverlay} ${isOpen ? styles.open : ''}`}
      onClick={handleBackdropClick}
    >
      <div className={styles.menuContainer}>
        {/* Header */}
        <div className={styles.menuHeader}>
          <div className={styles.userInfo}>
            <div className={styles.userAvatar}>
              {userName.charAt(0).toUpperCase()}
            </div>
            <div className={styles.userDetails}>
              <h3 className={styles.userName}>{userName}</h3>
              <span className={styles.userRole}>{userRole}</span>
            </div>
          </div>
          <button
            className={styles.closeButton}
            onClick={onClose}
            aria-label="Close menu"
          >
            ✕
          </button>
        </div>

        {/* Menu Content */}
        <div className={styles.menuContent}>
          {Object.entries(groupedItems).map(([section, items]) => (
            <div key={section} className={styles.menuSection}>
              <h4 className={styles.sectionTitle}>{section}</h4>
              <div className={styles.sectionItems}>
                {items.map((item) => (
                  <Link
                    key={item.id}
                    href={item.href}
                    className={`${styles.menuItem} ${isActive(item.href) ? styles.active : ''}`}
                    onClick={() => handleMenuItemClick(item)}
                  >
                    <div className={styles.menuItemIcon}>
                      {item.icon}
                    </div>
                    <span className={styles.menuItemLabel}>
                      {item.label}
                    </span>
                    {isActive(item.href) && (
                      <div className={styles.activeIndicator} />
                    )}
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className={styles.menuFooter}>
          <div className={styles.appInfo}>
            <span className={styles.appName}>Ocean Soul Sparkles Admin</span>
            <span className={styles.appVersion}>v1.0.0</span>
          </div>
        </div>
      </div>
    </div>
  );
}
