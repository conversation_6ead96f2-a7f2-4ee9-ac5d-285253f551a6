"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatalogItem = void 0;
const serializers = __importStar(require("../index"));
const core = __importStar(require("../../core"));
const CatalogItemModifierListInfo_1 = require("./CatalogItemModifierListInfo");
const CatalogItemProductType_1 = require("./CatalogItemProductType");
const CatalogItemOptionForItem_1 = require("./CatalogItemOptionForItem");
const CatalogEcomSeoData_1 = require("./CatalogEcomSeoData");
const CatalogItemFoodAndBeverageDetails_1 = require("./CatalogItemFoodAndBeverageDetails");
exports.CatalogItem = core.serialization.object({
    name: core.serialization.string().optionalNullable(),
    description: core.serialization.string().optionalNullable(),
    abbreviation: core.serialization.string().optionalNullable(),
    labelColor: core.serialization.property("label_color", core.serialization.string().optionalNullable()),
    isTaxable: core.serialization.property("is_taxable", core.serialization.boolean().optionalNullable()),
    categoryId: core.serialization.property("category_id", core.serialization.string().optionalNullable()),
    taxIds: core.serialization.property("tax_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    modifierListInfo: core.serialization.property("modifier_list_info", core.serialization.list(CatalogItemModifierListInfo_1.CatalogItemModifierListInfo).optionalNullable()),
    variations: core.serialization
        .list(core.serialization.lazy(() => serializers.CatalogObject))
        .optionalNullable(),
    productType: core.serialization.property("product_type", CatalogItemProductType_1.CatalogItemProductType.optional()),
    skipModifierScreen: core.serialization.property("skip_modifier_screen", core.serialization.boolean().optionalNullable()),
    itemOptions: core.serialization.property("item_options", core.serialization.list(CatalogItemOptionForItem_1.CatalogItemOptionForItem).optionalNullable()),
    ecomUri: core.serialization.property("ecom_uri", core.serialization.string().optionalNullable()),
    ecomImageUris: core.serialization.property("ecom_image_uris", core.serialization.list(core.serialization.string()).optionalNullable()),
    imageIds: core.serialization.property("image_ids", core.serialization.list(core.serialization.string()).optionalNullable()),
    sortName: core.serialization.property("sort_name", core.serialization.string().optionalNullable()),
    categories: core.serialization
        .list(core.serialization.lazyObject(() => serializers.CatalogObjectCategory))
        .optionalNullable(),
    descriptionHtml: core.serialization.property("description_html", core.serialization.string().optionalNullable()),
    descriptionPlaintext: core.serialization.property("description_plaintext", core.serialization.string().optional()),
    channels: core.serialization.list(core.serialization.string()).optionalNullable(),
    isArchived: core.serialization.property("is_archived", core.serialization.boolean().optionalNullable()),
    ecomSeoData: core.serialization.property("ecom_seo_data", CatalogEcomSeoData_1.CatalogEcomSeoData.optional()),
    foodAndBeverageDetails: core.serialization.property("food_and_beverage_details", CatalogItemFoodAndBeverageDetails_1.CatalogItemFoodAndBeverageDetails.optional()),
    reportingCategory: core.serialization.property("reporting_category", core.serialization.lazyObject(() => serializers.CatalogObjectCategory).optional()),
    isAlcoholic: core.serialization.property("is_alcoholic", core.serialization.boolean().optionalNullable()),
});
