"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Component = void 0;
const core = __importStar(require("../../core"));
const ComponentComponentType_1 = require("./ComponentComponentType");
const DeviceComponentDetailsApplicationDetails_1 = require("./DeviceComponentDetailsApplicationDetails");
const DeviceComponentDetailsCardReaderDetails_1 = require("./DeviceComponentDetailsCardReaderDetails");
const DeviceComponentDetailsBatteryDetails_1 = require("./DeviceComponentDetailsBatteryDetails");
const DeviceComponentDetailsWiFiDetails_1 = require("./DeviceComponentDetailsWiFiDetails");
const DeviceComponentDetailsEthernetDetails_1 = require("./DeviceComponentDetailsEthernetDetails");
exports.Component = core.serialization.object({
    type: ComponentComponentType_1.ComponentComponentType,
    applicationDetails: core.serialization.property("application_details", DeviceComponentDetailsApplicationDetails_1.DeviceComponentDetailsApplicationDetails.optional()),
    cardReaderDetails: core.serialization.property("card_reader_details", DeviceComponentDetailsCardReaderDetails_1.DeviceComponentDetailsCardReaderDetails.optional()),
    batteryDetails: core.serialization.property("battery_details", DeviceComponentDetailsBatteryDetails_1.DeviceComponentDetailsBatteryDetails.optional()),
    wifiDetails: core.serialization.property("wifi_details", DeviceComponentDetailsWiFiDetails_1.DeviceComponentDetailsWiFiDetails.optional()),
    ethernetDetails: core.serialization.property("ethernet_details", DeviceComponentDetailsEthernetDetails_1.DeviceComponentDetailsEthernetDetails.optional()),
});
