"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeviceCode = void 0;
const core = __importStar(require("../../core"));
const ProductType_1 = require("./ProductType");
const DeviceCodeStatus_1 = require("./DeviceCodeStatus");
exports.DeviceCode = core.serialization.object({
    id: core.serialization.string().optional(),
    name: core.serialization.string().optionalNullable(),
    code: core.serialization.string().optional(),
    deviceId: core.serialization.property("device_id", core.serialization.string().optional()),
    productType: core.serialization.property("product_type", ProductType_1.ProductType),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    status: DeviceCodeStatus_1.DeviceCodeStatus.optional(),
    pairBy: core.serialization.property("pair_by", core.serialization.string().optional()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    statusChangedAt: core.serialization.property("status_changed_at", core.serialization.string().optional()),
    pairedAt: core.serialization.property("paired_at", core.serialization.string().optional()),
});
