{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/admin/artists": "pages/admin/artists.js", "/admin/artists/portfolio": "pages/admin/artists/portfolio.js", "/admin/artists/[id]/portfolio": "pages/admin/artists/[id]/portfolio.js", "/admin/bookings": "pages/admin/bookings.js", "/admin/bookings/[id]": "pages/admin/bookings/[id].js", "/admin/bookings/new": "pages/admin/bookings/new.js", "/admin/communications": "pages/admin/communications.js", "/admin/customers-new": "pages/admin/customers-new.js", "/admin/customers": "pages/admin/customers.js", "/admin/customers/new": "pages/admin/customers/new.js", "/admin/customers/[id]": "pages/admin/customers/[id].js", "/admin/dashboard": "pages/admin/dashboard.js", "/admin/email-templates": "pages/admin/email-templates.js", "/admin/feedback": "pages/admin/feedback.js", "/admin/inventory": "pages/admin/inventory.js", "/admin/login": "pages/admin/login.js", "/admin/notifications": "pages/admin/notifications.js", "/admin/pos": "pages/admin/pos.js", "/admin/products": "pages/admin/products.js", "/admin/purchase-orders": "pages/admin/purchase-orders.js", "/admin/purchase-orders/new": "pages/admin/purchase-orders/new.js", "/admin/receipts": "pages/admin/receipts.js", "/admin/reports": "pages/admin/reports.js", "/admin/services": "pages/admin/services.js", "/admin/services/[id]": "pages/admin/services/[id].js", "/admin/services/[id]/edit": "pages/admin/services/[id]/edit.js", "/admin/services/new": "pages/admin/services/new.js", "/admin/settings": "pages/admin/settings.js", "/admin/sms-templates": "pages/admin/sms-templates.js", "/admin/staff": "pages/admin/staff.js", "/admin/staff/onboarding": "pages/admin/staff/onboarding.js", "/admin/staff/performance": "pages/admin/staff/performance.js", "/admin/staff/training": "pages/admin/staff/training.js", "/admin/suppliers": "pages/admin/suppliers.js", "/admin/suppliers/new": "pages/admin/suppliers/new.js", "/admin/tips": "pages/admin/tips.js", "/api/admin/artists": "pages/api/admin/artists.js", "/api/admin/artists/[id]/commissions": "pages/api/admin/artists/[id]/commissions.js", "/api/admin/artists/[id]/portfolio": "pages/api/admin/artists/[id]/portfolio.js", "/api/admin/artists/commissions": "pages/api/admin/artists/commissions.js", "/api/admin/artists/schedule": "pages/api/admin/artists/schedule.js", "/api/admin/bookings": "pages/api/admin/bookings.js", "/api/admin/communications": "pages/api/admin/communications.js", "/api/admin/bookings/[id]": "pages/api/admin/bookings/[id].js", "/api/admin/customers": "pages/api/admin/customers.js", "/api/admin/customers/[id]": "pages/api/admin/customers/[id].js", "/api/admin/customers/[id]/bookings": "pages/api/admin/customers/[id]/bookings.js", "/api/admin/dashboard": "pages/api/admin/dashboard.js", "/api/admin/email-templates": "pages/api/admin/email-templates.js", "/api/admin/email-templates/[id]": "pages/api/admin/email-templates/[id].js", "/api/admin/feedback": "pages/api/admin/feedback.js", "/api/admin/inventory": "pages/api/admin/inventory.js", "/api/admin/inventory/alerts": "pages/api/admin/inventory/alerts.js", "/api/admin/notifications/email": "pages/api/admin/notifications/email.js", "/api/admin/notifications/sms": "pages/api/admin/notifications/sms.js", "/api/admin/pos/artist-availability": "pages/api/admin/pos/artist-availability.js", "/api/admin/pos/create-booking": "pages/api/admin/pos/create-booking.js", "/api/admin/pos/process-payment": "pages/api/admin/pos/process-payment.js", "/api/admin/pos/terminal-checkout": "pages/api/admin/pos/terminal-checkout.js", "/api/admin/pos/terminal-devices": "pages/api/admin/pos/terminal-devices.js", "/api/admin/products": "pages/api/admin/products.js", "/api/admin/purchase-orders": "pages/api/admin/purchase-orders.js", "/api/admin/purchase-orders/[id]": "pages/api/admin/purchase-orders/[id].js", "/api/admin/receipts": "pages/api/admin/receipts.js", "/api/admin/purchase-orders/[id]/receive": "pages/api/admin/purchase-orders/[id]/receive.js", "/api/admin/receipts/preview": "pages/api/admin/receipts/preview.js", "/api/admin/reports": "pages/api/admin/reports.js", "/api/admin/reports/export": "pages/api/admin/reports/export.js", "/api/admin/services": "pages/api/admin/services.js", "/api/admin/services/[id]": "pages/api/admin/services/[id].js", "/api/admin/services/[id]/tiers": "pages/api/admin/services/[id]/tiers.js", "/api/admin/settings": "pages/api/admin/settings.js", "/api/admin/sms-templates": "pages/api/admin/sms-templates.js", "/api/admin/staff": "pages/api/admin/staff.js", "/api/admin/staff/onboarding": "pages/api/admin/staff/onboarding.js", "/api/admin/staff/performance": "pages/api/admin/staff/performance.js", "/api/admin/staff/schedule": "pages/api/admin/staff/schedule.js", "/api/admin/staff/training": "pages/api/admin/staff/training.js", "/api/admin/suppliers": "pages/api/admin/suppliers.js", "/api/auth/login": "pages/api/auth/login.js", "/api/admin/tips": "pages/api/admin/tips.js", "/api/admin/suppliers/[id]": "pages/api/admin/suppliers/[id].js", "/api/auth/logout": "pages/api/auth/logout.js", "/api/auth/mfa-verify": "pages/api/auth/mfa-verify.js", "/api/auth/verify": "pages/api/auth/verify.js", "/": "pages/index.js", "/api/admin/artists/portfolio": "pages/api/admin/artists/portfolio.js", "/_document": "pages/_document.js"}