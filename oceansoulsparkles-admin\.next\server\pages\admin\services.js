(()=>{var e={};e.id=9148,e.ids=[9148,660],e.modules={9611:e=>{e.exports={servicesContainer:"Services_servicesContainer__8QxNu",header:"Services_header__IbB5h",title:"Services_title__EjFUH",headerActions:"Services_headerActions__szgs7",newServiceBtn:"Services_newServiceBtn__CY17p",backButton:"Services_backButton__7_3qx",controlsPanel:"Services_controlsPanel__aS7XE",searchSection:"Services_searchSection__F9zbf",searchInput:"Services_searchInput__hPj0n",filters:"Services_filters__fAGdk",categoryFilter:"Services_categoryFilter__Z4V0s",sortSelect:"Services_sortSelect__u7Aoe",servicesContent:"Services_servicesContent__w8Z_j",statsCards:"Services_statsCards__94oSk",statCard:"Services_statCard__oNCIx",statValue:"Services_statValue__7ZjYH",emptyState:"Services_emptyState__0i9rz",servicesGrid:"Services_servicesGrid__7m04a",serviceCard:"Services_serviceCard__E_hZS",serviceHeader:"Services_serviceHeader__cWEP9",serviceInfo:"Services_serviceInfo__M_x0w",category:"Services_category__bAB21",statusBadge:"Services_statusBadge__l2Nef",active:"Services_active__5C_7j",inactive:"Services_inactive__HhFHN",serviceDetails:"Services_serviceDetails__bhQDF",description:"Services_description__vsrev",serviceStats:"Services_serviceStats__lZFEQ",statItem:"Services_statItem__NtyhU",statLabel:"Services_statLabel__Hbj1F",serviceActions:"Services_serviceActions__gNBFO",editBtn:"Services_editBtn__W9iEM",toggleBtn:"Services_toggleBtn__Geg7f",activate:"Services_activate__zeoXn",deactivate:"Services_deactivate__aeEHN",loadingContainer:"Services_loadingContainer__HBsMm",loadingSpinner:"Services_loadingSpinner__bBh3i",spin:"Services_spin__M5QLZ"}},1289:(e,s,i)=>{"use strict";i.a(e,async(e,r)=>{try{i.r(s),i.d(s,{config:()=>m,default:()=>_,getServerSideProps:()=>S,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>p,routeModule:()=>f,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>x,unstable_getStaticProps:()=>g});var t=i(7093),a=i(5244),c=i(1323),n=i(2899),l=i.n(n),d=i(6814),o=i(6034),v=e([d,o]);[d,o]=v.then?(await v)():v;let _=(0,c.l)(o,"default"),u=(0,c.l)(o,"getStaticProps"),h=(0,c.l)(o,"getStaticPaths"),S=(0,c.l)(o,"getServerSideProps"),m=(0,c.l)(o,"config"),p=(0,c.l)(o,"reportWebVitals"),g=(0,c.l)(o,"unstable_getStaticProps"),x=(0,c.l)(o,"unstable_getStaticPaths"),j=(0,c.l)(o,"unstable_getStaticParams"),N=(0,c.l)(o,"unstable_getServerProps"),b=(0,c.l)(o,"unstable_getServerSideProps"),f=new t.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/services",pathname:"/admin/services",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:o});r()}catch(e){r(e)}})},6034:(e,s,i)=>{"use strict";i.a(e,async(e,r)=>{try{i.r(s),i.d(s,{default:()=>S});var t=i(997),a=i(6689),c=i(968),n=i.n(c),l=i(1664),d=i.n(l),o=i(8568),v=i(4845),_=i(9611),u=i.n(_),h=e([v]);function S(){let{user:e,loading:s}=(0,o.a)(),[i,r]=(0,a.useState)(!0),[c,l]=(0,a.useState)([]),[_,h]=(0,a.useState)([]),[S,m]=(0,a.useState)(""),[p,g]=(0,a.useState)("all"),[x,j]=(0,a.useState)("name"),N=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),b=e=>{if(e>=60){let s=Math.floor(e/60),i=e%60;return i>0?`${s}h ${i}m`:`${s}h`}return`${e}m`};return s||i?t.jsx(v.Z,{children:(0,t.jsxs)("div",{className:u().loadingContainer,children:[t.jsx("div",{className:u().loadingSpinner}),t.jsx("p",{children:"Loading services..."})]})}):e?(0,t.jsxs)(v.Z,{children:[(0,t.jsxs)(n(),{children:[t.jsx("title",{children:"Services Management | Ocean Soul Sparkles Admin"}),t.jsx("meta",{name:"description",content:"Manage service catalog and pricing"})]}),(0,t.jsxs)("div",{className:u().servicesContainer,children:[(0,t.jsxs)("header",{className:u().header,children:[t.jsx("h1",{className:u().title,children:"Services Management"}),t.jsx("div",{className:u().headerActions,children:t.jsx(d(),{href:"/admin/services/new",className:u().newServiceBtn,children:"+ Add Service"})})]}),(0,t.jsxs)("div",{className:u().controlsPanel,children:[t.jsx("div",{className:u().searchSection,children:t.jsx("input",{type:"text",placeholder:"Search services by name, category, or description...",value:S,onChange:e=>m(e.target.value),className:u().searchInput})}),(0,t.jsxs)("div",{className:u().filtersSection,children:[(0,t.jsxs)("div",{className:u().filterGroup,children:[t.jsx("label",{children:"Category:"}),(0,t.jsxs)("select",{value:p,onChange:e=>g(e.target.value),className:u().filterSelect,children:[t.jsx("option",{value:"all",children:"All Categories"}),t.jsx("option",{value:"Hair Braiding",children:"Hair Braiding"}),t.jsx("option",{value:"Protective Styles",children:"Protective Styles"}),t.jsx("option",{value:"Hair Care",children:"Hair Care"}),t.jsx("option",{value:"Styling",children:"Styling"}),t.jsx("option",{value:"Consultation",children:"Consultation"})]})]}),(0,t.jsxs)("div",{className:u().filterGroup,children:[t.jsx("label",{children:"Sort by:"}),(0,t.jsxs)("select",{value:x,onChange:e=>j(e.target.value),className:u().filterSelect,children:[t.jsx("option",{value:"name",children:"Name"}),t.jsx("option",{value:"category",children:"Category"}),t.jsx("option",{value:"price",children:"Price"}),t.jsx("option",{value:"duration",children:"Duration"})]})]})]})]}),t.jsx("div",{className:u().servicesContent,children:0===_.length?(0,t.jsxs)("div",{className:u().emptyState,children:[t.jsx("h3",{children:"No services found"}),t.jsx("p",{children:0===c.length?"Get started by adding your first service to the catalog.":"Try adjusting your search or filter criteria."}),t.jsx(d(),{href:"/admin/services/new",className:u().addFirstBtn,children:"Add First Service"})]}):t.jsx("div",{className:u().servicesGrid,children:_.map(e=>(0,t.jsxs)("div",{className:u().serviceCard,children:[(0,t.jsxs)("div",{className:u().cardHeader,children:[t.jsx("h3",{className:u().serviceName,children:e.name}),t.jsx("span",{className:u().categoryBadge,children:e.category})]}),(0,t.jsxs)("div",{className:u().cardBody,children:[e.description&&t.jsx("p",{className:u().description,children:e.description}),(0,t.jsxs)("div",{className:u().serviceDetails,children:[(0,t.jsxs)("div",{className:u().priceInfo,children:[t.jsx("span",{className:u().label,children:"Price:"}),t.jsx("span",{className:u().value,children:e.price?N(e.price):"Contact for pricing"})]}),e.duration&&(0,t.jsxs)("div",{className:u().durationInfo,children:[t.jsx("span",{className:u().label,children:"Duration:"}),t.jsx("span",{className:u().value,children:b(e.duration)})]}),e.requirements&&(0,t.jsxs)("div",{className:u().requirementsInfo,children:[t.jsx("span",{className:u().label,children:"Requirements:"}),t.jsx("span",{className:u().value,children:e.requirements})]})]}),e.images&&e.images.length>0&&t.jsx("div",{className:u().serviceImages,children:(0,t.jsxs)("div",{className:u().imageCount,children:[e.images.length," image",e.images.length>1?"s":""]})})]}),(0,t.jsxs)("div",{className:u().cardActions,children:[t.jsx(d(),{href:`/admin/services/${e.id}`,className:u().viewBtn,children:"View Details"}),t.jsx(d(),{href:`/admin/services/${e.id}/edit`,className:u().editBtn,children:"Edit"}),t.jsx("button",{className:u().toggleBtn,title:e.active?"Disable service":"Enable service",children:e.active?"Disable":"Enable"})]})]},e.id))})})]})]}):null}v=(h.then?(await h)():h)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var i=e=>s(s.s=e),r=s.X(0,[2899,6212,1664,7441],()=>i(1289));module.exports=r})();