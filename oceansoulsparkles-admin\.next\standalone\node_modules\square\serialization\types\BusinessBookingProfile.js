"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessBookingProfile = void 0;
const core = __importStar(require("../../core"));
const BusinessBookingProfileCustomerTimezoneChoice_1 = require("./BusinessBookingProfileCustomerTimezoneChoice");
const BusinessBookingProfileBookingPolicy_1 = require("./BusinessBookingProfileBookingPolicy");
const BusinessAppointmentSettings_1 = require("./BusinessAppointmentSettings");
exports.BusinessBookingProfile = core.serialization.object({
    sellerId: core.serialization.property("seller_id", core.serialization.string().optionalNullable()),
    createdAt: core.serialization.property("created_at", core.serialization.string().optional()),
    bookingEnabled: core.serialization.property("booking_enabled", core.serialization.boolean().optionalNullable()),
    customerTimezoneChoice: core.serialization.property("customer_timezone_choice", BusinessBookingProfileCustomerTimezoneChoice_1.BusinessBookingProfileCustomerTimezoneChoice.optional()),
    bookingPolicy: core.serialization.property("booking_policy", BusinessBookingProfileBookingPolicy_1.BusinessBookingProfileBookingPolicy.optional()),
    allowUserCancel: core.serialization.property("allow_user_cancel", core.serialization.boolean().optionalNullable()),
    businessAppointmentSettings: core.serialization.property("business_appointment_settings", BusinessAppointmentSettings_1.BusinessAppointmentSettings.optional()),
    supportSellerLevelWrites: core.serialization.property("support_seller_level_writes", core.serialization.boolean().optionalNullable()),
});
